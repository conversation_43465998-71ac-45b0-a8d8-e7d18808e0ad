import { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
// import { FiInfo } from "react-icons/fi";
import Button from '../Field/Button';
import { motion, AnimatePresence } from 'framer-motion';
import { Info, X } from 'lucide-react';

const PopUp = ({
  title,
  width = 'md',
  children,
  isDelete = false,
  isEdit = false,
  isDisabled = false,
  isScrollable = false,
  post,
  onClose
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300);
  };

  const widthClasses = {
    sm: 'w-[40vw]',
    md: 'w-[50vw]',
    lg: 'w-[60vw]',
    xl: 'w-[90vw]'
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/60 backdrop-blur-md flex items-center justify-center z-40 transition-opacity duration-300 "
        >
          <motion.div
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className={`bg-white/80 rounded-lg shadow-xl max-h-[100vh] transform transition-transform duration-300 ${widthClasses[width]}`}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-4 rounded-t-lg flex justify-between items-center gap-2 border-b-2 border-black/10 sticky top-0">
              <div className="flex justify-start items-center gap-2">
                <div>
                  <Info color="blue" size={28} />
                </div>
                <h2
                  className="text-xl sm:text-2xl font-semibold text-gray-800 line-clamp-2"
                  title={title}
                >
                  {title}
                </h2>
              </div>
              <div>
                <Button
                  onClick={handleClose}
                  className="text-gray-500 hover:text-gray-700 cursor-pointer"
                  icon={<X />}
                />
              </div>
            </div>

            <div
              className={`p-4 ${isScrollable ? 'max-h-[80vh] overflow-y-auto' : 'overflow-visible'}`}
            >
              {children}
            </div>
            {post && (
              <div className="flex justify-end items-center gap-4 p-2 rounded-b-lg border-t-2 border-black/10">
                <Button
                  name={isDelete ? 'Delete' : isEdit ? 'Update' : 'Save'}
                  className={`px-5 py-2 text-sm font-medium rounded-md shadow-sm transition-colors ${
                    isDisabled
                      ? 'cursor-not-allowed bg-gray-300 text-gray-500'
                      : 'bg-green-600 hover:bg-green-700 text-white'
                  }`}
                  onClick={post}
                  disabled={isDisabled}
                />
                <Button
                  name="Close"
                  className="px-5 py-2 text-sm font-medium rounded-md shadow-sm transition-colors bg-red-600 hover:bg-red-700 text-white"
                  onClick={handleClose}
                />
              </div>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

PopUp.propTypes = {
  title: PropTypes.string.isRequired,
  width: PropTypes.oneOf(['sm', 'md', 'lg', 'xl']),
  children: PropTypes.node.isRequired,
  isDelete: PropTypes.bool,
  isEdit: PropTypes.bool,
  isDisabled: PropTypes.bool,
  isScrollable: PropTypes.bool,
  post: PropTypes.func,
  onClose: PropTypes.func.isRequired
};

export default PopUp;
