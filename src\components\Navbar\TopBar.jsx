import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { clearAuthData, useUserLogoutServiceMutation } from '../../pages/auth/auth.slice';
import { clearMenuData } from './navbar.slice';
import { LogOut, Menu, X, Smile, User, Search } from 'lucide-react';
import Toastify from '../PopUp/Toastify';
import { motion, AnimatePresence, useAnimation, useMotionValue, useTransform } from 'framer-motion';
import Logo from '../../assets/sasthra_logo.png';
import NotificationDropdown from './NotificationDropdown';

const TopBar = ({ onToggleSidebar }) => {
  const roleColorMap = {
    director: 'bg-director',
    student: 'bg-student',
    center_counselor: 'bg-counselor',
    kota_teacher: 'bg-teacher',
    faculty: 'bg-trainee',
    parent: 'bg-parents',
    mendor: 'bg-mentor'
  };

  const [res, setRes] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [notifications, setNotifications] = useState([]);
  const [showNotifications, setShowNotifications] = useState(false);
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [unreadNotifications, setUnreadNotifications] = useState(3);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [userLogout] = useUserLogoutServiceMutation();
  const controls = useAnimation();
  const profileRef = useRef(null);
  const dragX = useMotionValue(0);
  const opacity = useTransform(dragX, [-100, 0, 100], [0.5, 1, 0.5]);

  const bgClass = roleColorMap[sessionStorage.role] || 'bg-white';

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (profileRef.current && !profileRef.current.contains(event.target)) {
        setIsProfileOpen(false);
        setShowNotifications(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Mock notifications
  useEffect(() => {
    setNotifications([
      { id: 1, text: 'New message from admin', time: '2 mins ago', read: false },
      { id: 2, text: 'Your profile needs update', time: '1 hour ago', read: false },
      { id: 3, text: 'System maintenance scheduled', time: 'Yesterday', read: true }
    ]);
  }, []);

  const handleLogout = async () => {
    try {
      const userId = sessionStorage.getItem('userId');
      if (userId) {
        await userLogout({ user_id: userId }).unwrap();
      }
      setRes({ message: 'Logout successful' });

      sessionStorage.clear();
      dispatch(clearAuthData());
      dispatch(clearMenuData());
      navigate('/auth', { replace: true });
    } catch (error) {
      console.error('Logout failed:', error);
      setRes(error);
      sessionStorage.clear();
      dispatch(clearAuthData());
      dispatch(clearMenuData());
      navigate('/auth', { replace: true });
    }
  };

  const handleNotificationClick = (id) => {
    setNotifications(notifications.map((n) => (n.id === id ? { ...n, read: true } : n)));
    setUnreadNotifications(notifications.filter((n) => !n.read && n.id !== id).length);
  };

  const username = sessionStorage.getItem('name') || 'User';

  return (
    <header
      className={`w-full ${bgClass} flex items-center justify-between px-6 h-20 shadow-md z-30 sticky top-0`}
    >
      <div className="flex items-center gap-4">
        <Toastify res={res} resClear={() => setRes(null)} />
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={onToggleSidebar}
          className="text-white p-2 rounded-full hover:bg-black/20 transition-colors"
        >
          <Menu size={24} />
        </motion.button>

        <motion.div whileHover={{ scale: 1.03 }}>
          <Link to="/sasthra">
            <img alt="Sasthra Logo" src={Logo} className="h-16 w-auto" />
          </Link>
        </motion.div>
      </div>

      {/* Search Bar */}
      <motion.div
        className="hidden md:flex items-center bg-white/20 rounded-full px-4 py-2 w-1/3 max-w-md"
        whileHover={{ scale: 1.02 }}
      >
        <Search className="text-white mr-2" size={18} />
        <input
          type="text"
          placeholder="Search..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="bg-transparent border-none outline-none text-white placeholder-white/70 w-full"
        />
      </motion.div>

      <div className="flex items-center gap-4">
        {/* Notifications */}
        <div ref={profileRef}>
          <NotificationDropdown
            notifications={notifications}
            showNotifications={showNotifications}
            setShowNotifications={setShowNotifications}
            setIsProfileOpen={setIsProfileOpen}
            handleNotificationClick={handleNotificationClick}
            unreadNotifications={unreadNotifications}
            setUnreadNotifications={setUnreadNotifications}
          />
        </div>

        {/* Profile Dropdown */}
        <div className="relative" ref={profileRef}>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => {
              setIsProfileOpen(!isProfileOpen);
              setShowNotifications(false);
            }}
            className="cursor-pointer relative"
          >
            <User className="text-white" size={30} />
            <motion.div
              animate={isProfileOpen ? { rotate: 180 } : { rotate: 0 }}
              className="absolute -bottom-2 -right-2 bg-white rounded-full p-1 shadow-sm"
            >
              <svg
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </motion.div>
          </motion.div>

          <AnimatePresence>
            {isProfileOpen && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ type: 'spring', damping: 20 }}
                className="absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-xl py-1 z-40 overflow-hidden"
              >
                <div className="px-4 py-3 border-b border-gray-100 flex items-center gap-3">
                  <User alt="User" className="w-10 h-10 text-[var(--color-teacher)] rounded-full" />
                  <div>
                    <p className="font-medium text-gray-900">{username}</p>
                    <p className="text-xs text-gray-500">{sessionStorage.role || 'User'}</p>
                  </div>
                </div>

                <motion.div
                  className="border-t border-gray-100"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  <motion.button
                    whileHover={{ backgroundColor: '#fef2f2' }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setIsConfirmOpen(true)}
                    className="w-full hover:cursor-pointer text-left flex items-center gap-2 px-4 py-3 text-sm text-red-600 transition-colors"
                  >
                    <LogOut size={16} className="text-red-500" />
                    <span>Logout</span>
                  </motion.button>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Logout Confirmation Modal */}
      <AnimatePresence>
        {isConfirmOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-[var(--color-teacher)]/40 backdrop-blur-sm p-4"
          >
            <motion.div
              initial={{ y: 40, opacity: 0, scale: 0.96 }}
              animate={{ y: 0, opacity: 1, scale: 1 }}
              exit={{ y: 40, opacity: 0, scale: 0.96 }}
              transition={{
                type: 'spring',
                damping: 15,
                stiffness: 300,
                bounce: 0.25
              }}
              className="relative max-w-sm w-full bg-[var(--color-teacher)] rounded-2xl shadow-xl overflow-hidden border border-gray-100"
            >
              <div className="absolute inset-0 overflow-hidden opacity-5">
                {[...Array(12)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute rounded-full bg-gray-400"
                    initial={{
                      x: Math.random() * 100,
                      y: Math.random() * 100,
                      width: Math.random() * 8 + 2,
                      height: Math.random() * 8 + 2
                    }}
                    animate={{
                      y: [null, Math.random() * 20 - 10]
                    }}
                    transition={{
                      duration: Math.random() * 4 + 3,
                      repeat: Infinity,
                      repeatType: 'reverse',
                      ease: 'easeInOut'
                    }}
                  />
                ))}
              </div>

              <div className="relative z-10 p-8">
                <motion.div
                  className="flex justify-between items-center mb-8"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <motion.h3
                    className="text-2xl font-semibold text-[var(--color-counselor)]"
                    animate={{
                      x: [0, 2, -2, 0]
                    }}
                    transition={{
                      duration: 6,
                      repeat: Infinity,
                      ease: 'easeInOut'
                    }}
                  >
                    Ready to leave?
                  </motion.h3>
                  <motion.button
                    whileHover={{ rotate: 90, backgroundColor: 'rgba(0,0,0,0.05)' }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setIsConfirmOpen(false)}
                    className="p-1 rounded-full text-white hover:text-white hover:cursor-pointer transition-all"
                  >
                    <X size={20} />
                  </motion.button>
                </motion.div>

                <motion.div
                  className="flex flex-col items-center mb-8"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  <div className="relative w-70 h-25 mb-4">
                    <motion.div
                      className="absolute inset-0 border-gray-200"
                      animate={{
                        scale: [1, 1.05, 1],
                        opacity: [0.8, 1, 0.8]
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        ease: 'easeInOut'
                      }}
                    />
                    <img
                      src={Logo}
                      alt="User"
                      className="w-full h-full rounded-full object-cover"
                    />
                  </div>
                  <p className="text-white text-center">
                    Do you want to Logout from <br />
                    <span className="font-medium text-[var(--color-counselor)]">
                      Sasthra Dashboard
                    </span>
                  </p>
                </motion.div>

                <div className="flex flex-col gap-3">
                  <motion.button
                    whileHover={{
                      scale: 1.02,
                      boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                      backgroundColor: '#f3f4f6'
                    }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setIsConfirmOpen(false)}
                    className="py-3 px-6 rounded-xl bg-gray-100 hover:cursor-pointer text-gray-700 font-medium transition-all relative overflow-hidden"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    <motion.span
                      className="absolute inset-0 bg-gradient-to-r from-transparent via-white/50 to-transparent"
                      initial={{ x: '-100%' }}
                      animate={{ x: '100%' }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        delay: 0.5
                      }}
                    />
                    <span className="relative z-10 flex items-center justify-center gap-2">
                      <span className="text-[var(--color-teacher)]">Continue Working</span>
                      <motion.div
                        animate={{ rotate: [0, 10, -10, 0] }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          repeatType: 'mirror'
                        }}
                      >
                        <Smile size={18} className="text-[var(--color-teacher)]" />
                      </motion.div>
                    </span>
                  </motion.button>

                  <motion.button
                    whileHover={{
                      scale: 1.02,
                      boxShadow: '0 4px 16px rgba(239,68,68,0.2)'
                    }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleLogout}
                    className="py-3 px-6 rounded-xl bg-[var(--color-counselor)] hover:cursor-pointer text-white font-medium transition-all relative overflow-hidden group"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                  >
                    <motion.span
                      className="absolute inset-0 bg-gradient-to-r from-transparent via-white/50 to-transparent"
                      initial={{ x: '-100%' }}
                      animate={{ x: '100%' }}
                      transition={{
                        duration: 2,
                        repeat: Infinity
                      }}
                    />
                    <span className="relative z-10 flex items-center justify-center gap-2">
                      <motion.span
                        animate={{ x: [0, 2, -2, 0] }}
                        transition={{
                          duration: 2,
                          repeat: Infinity
                        }}
                      >
                        Sign Out
                      </motion.span>
                      <motion.div
                        animate={{
                          x: [0, 4, 0],
                          rotate: [0, 360]
                        }}
                        transition={{
                          duration: 1.5,
                          repeat: Infinity,
                          repeatType: 'reverse'
                        }}
                      >
                        <LogOut size={18} />
                      </motion.div>
                    </span>
                  </motion.button>
                </div>
              </div>

              <motion.div
                className="absolute top-4 right-4 w-3 h-3 rounded-full bg-red-400"
                animate={{
                  y: [0, -8, 0],
                  opacity: [0.6, 1, 0.6]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: 'easeInOut'
                }}
              />
              <motion.div
                className="absolute bottom-6 left-6 w-2 h-2 rounded-full bg-blue-400"
                animate={{
                  y: [0, -6, 0],
                  opacity: [0.4, 0.8, 0.4]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: 'easeInOut',
                  delay: 0.5
                }}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
};

export default TopBar;
