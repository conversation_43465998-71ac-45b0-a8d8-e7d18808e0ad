import React, { useState, memo, useRef, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import {
  clearSimulationContent,
  setLearnPracticallyData,
  setSimulationContent,
  useLazyGetLearnPracticallyServiceByIdQuery,
  useLazyGetLearnPracticallyServiceQuery
} from './learnPractically.slice'; // NOTE: This import assumes the file exists in your project structure
import { ArrowLeft, Loader, SearchX, Pointer, ChevronDown, Check } from 'lucide-react';

// --- Reusable UI Components (Memoized for Performance) ---

/**
 * Memoized Placeholder: A versatile, animated placeholder for various states.
 */
const DetailPlaceholder = memo(({ icon, title, message }) => (
  <motion.div
    key="placeholder"
    initial={{ opacity: 0, scale: 0.95 }}
    animate={{ opacity: 1, scale: 1 }}
    exit={{ opacity: 0, scale: 0.95 }}
    transition={{ type: 'spring', damping: 15, stiffness: 100 }}
    className="text-center text-gray-500 p-8 flex flex-col items-center justify-center h-full"
  >
    <div className="text-gray-400 mb-4">{icon}</div>
    <h3 className="text-xl font-semibold text-gray-700">{title}</h3>
    <p className="max-w-xs text-center mt-1">{message}</p>
  </motion.div>
));

/**
 * Memoized Subject Card: The core interactive element on the main screen.
 */
const SubjectCard = memo(({ subject, onSelect }) => (
  <motion.div
    layoutId={`card-container-${subject.subjectId}`}
    className="bg-white border border-gray-200 rounded-2xl p-6 flex flex-col justify-between shadow-md hover:shadow-xl hover:border-blue-400/50 transition-shadow duration-300"
    whileHover={{ y: -8 }}
    whileTap={{ scale: 0.97 }}
    transition={{ type: 'spring', damping: 15, stiffness: 200 }}
  >
    <div className="flex justify-start items-center mb-6">
      <motion.div layoutId={`card-icon-${subject.subjectId}`} className={`inline-block text-4xl `}>
        {subject.icon}
      </motion.div>
      <motion.h2
        layoutId={`card-title-${subject.subjectId}`}
        className="text-2xl font-bold text-gray-800"
      >
        {subject.name}
      </motion.h2>
    </div>
    <p className="text-gray-600 mt-2 mb-6">{subject.description}</p>
    <motion.button
      onClick={() => onSelect(subject)}
      className="bg-blue-600 text-white w-full py-3 font-semibold rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
    >
      Explore Simulations
    </motion.button>
  </motion.div>
));

/**
 * Custom, animated dropdown component for the header.
 */
const SimulationDropdown = memo(({ options, selected, onSelect, isLoading }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSelect = (option) => {
    onSelect(option);
    setIsOpen(false);
  };

  return (
    <div className="relative w-full max-w-xs ml-auto" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isLoading || !options || options.length === 0}
        className="flex items-center justify-between w-full px-4 py-2.5 text-left bg-gray-100 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-60 disabled:cursor-not-allowed transition-colors"
      >
        <span className="truncate pr-2">
          {isLoading ? 'Loading...' : selected?.filename || 'Select a Simulation'}
        </span>
        <motion.div animate={{ rotate: isOpen ? 180 : 0 }}>
          <ChevronDown className="h-5 w-5 text-gray-500" />
        </motion.div>
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute z-10 w-full mt-2 bg-white border border-gray-200 rounded-lg shadow-xl overflow-hidden"
          >
            <ul className="max-h-60 overflow-y-auto">
              {options?.map((option) => (
                <li key={option._id}>
                  <button
                    onClick={() => handleSelect(option)}
                    className="w-full text-left px-4 py-3 text-gray-700 hover:bg-blue-500 hover:text-white flex items-center justify-between transition-colors group"
                  >
                    <span className="truncate">{option.filename}</span>
                    {selected?._id === option._id && (
                      <Check className="h-5 w-5 text-blue-500 group-hover:text-white" />
                    )}
                  </button>
                </li>
              ))}
            </ul>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
});

// --- Data (Constants) ---
const subjects = [
  {
    name: 'Physics',
    subjectId: '1',
    subName: 'physics',
    description: 'Explore mechanics, thermodynamics, and quantum physics.',
    color: 'bg-blue-500',
    icon: '⚛️'
  },
  {
    name: 'Chemistry',
    subjectId: '2',
    subName: 'chemistry',
    description: 'Discover molecular structures and chemical reactions.',
    color: 'bg-green-500',
    icon: '🧪'
  },
  {
    name: 'Biology',
    subjectId: '5',
    subName: 'biology',
    description: 'Study living organisms and life processes.',
    color: 'bg-emerald-500',
    icon: '🧬'
  },
  {
    name: 'Math & Statistics',
    subjectId: '3',
    subName: 'math& statics',
    description: 'Master mathematical concepts and statistical analysis.',
    color: 'bg-purple-500',
    icon: '📊'
  },
  {
    name: 'Earth & Space',
    subjectId: '4',
    subName: 'earth& space',
    description: 'Explore our planet and the cosmos beyond.',
    color: 'bg-orange-500',
    icon: '🌍'
  }
];

// --- Animation Variants ---
const springTransition = { type: 'spring', damping: 20, stiffness: 150 };
const listContainerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.08, delayChildren: 0.1 } }
};
const listItemVariants = {
  hidden: { y: 30, opacity: 0 },
  visible: { y: 0, opacity: 1, transition: springTransition }
};

/**
 * A feature component for exploring interactive science simulations.
 */
const LearnPractically = () => {
  const dispatch = useDispatch();

  // --- State Management ---
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [selectedSimulation, setSelectedSimulation] = useState(null);
  const [isIframeContentLoading, setIsIframeContentLoading] = useState(true);

  // --- Redux State & RTK Query Hooks ---
  const subjectData = useSelector((state) => state.learnPractically.learnPracticallyData);
  const simulationContent = useSelector((state) => state.learnPractically.simulationContent);

  const [fetchSubjects, { isLoading: areSubjectsLoading }] =
    useLazyGetLearnPracticallyServiceQuery();
  const [fetchSimulationById, { isLoading: isSimulationContentFetching }] =
    useLazyGetLearnPracticallyServiceByIdQuery();

  // --- Event Handlers (Memoized with useCallback) ---

  /**
   * Fetches content for a given simulation and subject.
   * This handler is designed to be called by other handlers, passing data directly.
   */
  const handleSelectSimulation = useCallback(
    async (simulation, subject) => {
      if (!simulation || !subject) return;
      if (selectedSimulation?._id === simulation._id) return;

      setIsIframeContentLoading(true);
      setSelectedSimulation(simulation);

      try {
        const res = await fetchSimulationById({
          subjectName: subject.subName,
          simulationId: simulation._id
        }).unwrap();
        dispatch(setSimulationContent(res.content));
      } catch (error) {
        console.error('Error selecting simulation:', error);
        dispatch(clearSimulationContent());
      }
    },
    [selectedSimulation, dispatch, fetchSimulationById]
  );

  /**
   * Handles clicking on a main subject card. Fetches the simulation list
   * and then automatically triggers the first simulation to load.
   */
  const handleGetSubject = useCallback(
    async (subject) => {
      setSelectedSubject(subject);
      dispatch(clearSimulationContent());

      try {
        const response = await fetchSubjects(subject.subName).unwrap();
        dispatch(setLearnPracticallyData(response));

        if (response?.length > 0) {
          // Auto-select the first item, passing the `subject` directly to avoid stale state
          handleSelectSimulation(response[0], subject);
        } else {
          setSelectedSimulation(null);
        }
      } catch (error) {
        console.error('Error fetching subject data:', error);
        dispatch(setLearnPracticallyData([]));
        setSelectedSimulation(null);
      }
    },
    [dispatch, fetchSubjects, handleSelectSimulation]
  );

  /**
   * A wrapper for the dropdown's onSelect. It provides the currently selected
   * subject from state to the main simulation handler.
   */
  const handleDropdownSelect = useCallback(
    (simulation) => {
      handleSelectSimulation(simulation, selectedSubject);
    },
    [handleSelectSimulation, selectedSubject]
  );

  const handleGoBack = useCallback(() => {
    setSelectedSubject(null);
    setSelectedSimulation(null);
    dispatch(setLearnPracticallyData(null));
    dispatch(clearSimulationContent());
  }, [dispatch]);

  const handleIframeLoad = useCallback(() => setIsIframeContentLoading(false), []);

  const isApiLoading = areSubjectsLoading || isSimulationContentFetching;

  return (
    <div className="p-4 min-h-screen bg-gray-50 font-sans antialiased">
      <AnimatePresence mode="wait" initial={false}>
        {selectedSubject ? (
          // --- DETAIL VIEW ---
          <motion.div key="detailView" className="max-w-7xl mx-auto">
            {/* Header */}
            <motion.div
              layoutId={`card-container-${selectedSubject.subjectId}`}
              transition={{ ...springTransition, duration: 0.6 }}
              className="bg-white rounded-2xl p-4 mb-6 flex items-center gap-4 shadow-lg"
            >
              <button
                onClick={handleGoBack}
                className="p-2 rounded-full hover:bg-gray-100 transition-colors shrink-0"
                aria-label="Go back to subjects"
              >
                <ArrowLeft className="h-6 w-6 text-gray-600" />
              </button>
              <motion.div
                layoutId={`card-icon-${selectedSubject.subjectId}`}
                transition={springTransition}
                className={`p-3 text-2xl ${selectedSubject.color} rounded-lg text-white shadow-md ${selectedSubject.color.replace('bg-', 'shadow-')}/40 shrink-0`}
              >
                {selectedSubject.icon}
              </motion.div>
              <motion.h2
                layoutId={`card-title-${selectedSubject.subjectId}`}
                transition={springTransition}
                className="text-2xl md:text-3xl font-bold text-gray-800 truncate"
              >
                {selectedSubject.name}
              </motion.h2>
              <SimulationDropdown
                isLoading={areSubjectsLoading}
                options={subjectData || []}
                selected={selectedSimulation}
                onSelect={handleDropdownSelect} // Use the wrapper function here
              />
            </motion.div>

            {/* Main Content Area */}
            <div className="flex flex-col lg:flex-row gap-6 h-[calc(100vh-160px)]">
              {/* Left Panel: Simulation Details */}
              <motion.div
                className="lg:w-1/3 bg-white border border-gray-200/80 rounded-2xl shadow-md p-6 flex flex-col"
                initial={{ x: -50, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ ...springTransition, delay: 0.3 }}
              >
                <AnimatePresence mode="wait">
                  {selectedSimulation ? (
                    <motion.div
                      key={selectedSimulation._id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.3 }}
                    >
                      <h3 className="text-xl font-bold mb-3 pb-3 border-b text-gray-800">
                        Instructions
                      </h3>
                      <h4 className="text-lg font-semibold text-blue-700 mb-2">
                        {selectedSimulation.filename}
                      </h4>
                      <p className="text-gray-600 leading-relaxed">
                        {selectedSimulation.description ||
                          'No specific instructions are available for this simulation.'}
                      </p>
                    </motion.div>
                  ) : (
                    <DetailPlaceholder
                      icon={<Pointer size={48} />}
                      title="Select a Simulation"
                      message="Choose a simulation from the dropdown above to begin."
                    />
                  )}
                </AnimatePresence>
              </motion.div>

              {/* Right Panel: Simulation Iframe */}
              <motion.div
                className="lg:w-2/3 bg-gray-200 border border-gray-300/50 rounded-2xl shadow-inner overflow-hidden flex items-center justify-center relative"
                initial={{ x: 50, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ ...springTransition, delay: 0.4 }}
              >
                <AnimatePresence mode="wait">
                  {/* Show loader while API is fetching OR iframe is still loading its own content */}
                  {(isIframeContentLoading || isApiLoading) && !simulationContent && (
                    <DetailPlaceholder
                      key="loader"
                      icon={<Loader size={48} className="animate-spin" />}
                      title="Loading Simulation..."
                      message="Getting things ready for you."
                    />
                  )}

                  {simulationContent && (
                    <motion.iframe
                      key={selectedSimulation._id} // Use a unique ID to force re-render
                      srcDoc={simulationContent}
                      title={selectedSimulation.filename}
                      onLoad={handleIframeLoad}
                      className="w-full h-full bg-white"
                      // Animate from invisible to visible only when iframe content is fully loaded
                      initial={{ opacity: 0 }}
                      animate={{ opacity: isIframeContentLoading ? 0 : 1 }}
                      transition={{ duration: 0.4 }}
                    />
                  )}

                  {/* Handle cases where there is no content to show after loading */}
                  {!isApiLoading && !simulationContent && (
                    <DetailPlaceholder
                      key="placeholder-no-content"
                      icon={<SearchX size={48} />}
                      title="No Simulation Available"
                      message="There might be an issue fetching this simulation, or none exist for this subject."
                    />
                  )}
                </AnimatePresence>
              </motion.div>
            </div>
          </motion.div>
        ) : (
          // --- MAIN SELECTION VIEW ---
          <motion.div key="subjectSelection" className="max-w-6xl mx-auto">
            <motion.div
              className="text-center mb-12"
              initial="hidden"
              animate="visible"
              variants={listContainerVariants}
            >
              <motion.h1
                variants={listItemVariants}
                className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
              >
                Learn Practically
              </motion.h1>
              <motion.p
                variants={listItemVariants}
                className="text-lg text-gray-600 max-w-2xl mx-auto"
              >
                Choose a subject to explore interactive simulations and enhance your learning
                experience.
              </motion.p>
            </motion.div>
            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
              variants={listContainerVariants}
              initial="hidden"
              animate="visible"
            >
              {subjects.map((sub) => (
                <motion.div key={sub.subjectId} variants={listItemVariants}>
                  <SubjectCard subject={sub} onSelect={handleGetSubject} />
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LearnPractically;
