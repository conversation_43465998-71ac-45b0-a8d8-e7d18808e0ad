import PropTypes from 'prop-types';
import { motion } from 'framer-motion';
import { useState } from 'react';

const Input = ({
  type,
  name,
  label,
  disabled,
  value,
  defaultValue,
  onChange,
  onClick,
  accept,
  max,
  min,
  maxLength,
  minLength,
  pattern,
  required,
  placeholder,
  className,
  error,
  errorMessage,
  leftIcon,
  rightIcon,
  checked,
  inputmode,
  multiple
}) => {
  const [fileName, setFileName] = useState('');

  // Handle File Input
  if (type === 'file') {
    const handleFileChange = (e) => {
      const files = e.target.files;
      setFileName(files.length > 1 ? `${files.length} files selected` : files[0]?.name || '');
      onChange && onChange(e);
    };

    return (
      <div className={`relative w-full ${className || ''}`}>
        {label && (
          <label htmlFor={name} className="tracking-wide block mb-1 font-semibold">
            {label}
            {required && <span className="text-lg font-bold text-red-600 ml-1">*</span>}
          </label>
        )}
        <div
          className={`flex items-center justify-between w-full px-4 py-3 border rounded-lg bg-white
          ${disabled ? 'cursor-not-allowed bg-gray-200' : 'cursor-pointer'}
          ${error ? 'border-red-500' : 'border-gray-300'}
        `}
        >
          <label htmlFor={name} className={`text-sm text-gray-700 w-full cursor-pointer`}>
            {fileName || placeholder || 'Choose file...'}
          </label>
          <input
            type="file"
            name={name}
            id={name}
            accept={accept}
            multiple={multiple}
            disabled={disabled}
            required={required}
            onChange={handleFileChange}
            onClick={onClick}
            className="hidden"
          />
          <span className="ml-4 text-indigo-600 font-semibold text-sm">Browse</span>
        </div>
        {error && (
          <span className="text-red-500 text-sm mt-1">
            {errorMessage || 'This field is required'}
          </span>
        )}
      </div>
    );
  }

  // Handle Checkbox Input
  if (type === 'checkbox') {
    return (
      <label className={`inline-flex items-center ${className || ''}`}>
        <input
          type="checkbox"
          name={name}
          disabled={disabled}
          checked={checked}
          onChange={onChange}
          onClick={onClick}
          className={`form-checkbox h-5 w-5 text-indigo-600 ${
            disabled ? 'cursor-not-allowed' : 'cursor-pointer'
          } rounded border-gray-300 focus:ring-indigo-500`}
        />
        {label && (
          <span className={`ml-2 text-black ${disabled ? 'text-gray-500' : ''}`}>
            {label}
            {required && (
              <span id="required" className="text-lg font-semibold text-red-600 ml-1">
                *
              </span>
            )}
          </span>
        )}
      </label>
    );
  }

  // Default Input
  return (
    <div className="relative inline-block w-full">
      {label && (
        <label htmlFor={name} className="tracking-wide block mb-1 font-semibold">
          {label}
          {required && (
            <span id="required" className="text-lg font-bold text-red-600 ml-1">
              *
            </span>
          )}
        </label>
      )}
      <motion.div
        animate={error ? { x: [0, -5, 5, -5, 5, 0] } : {}}
        transition={{ duration: 0.3 }}
        className="relative flex items-center"
      >
        {leftIcon && (
          <span className="absolute inset-y-0 left-3 flex items-center text-gray-500">
            {leftIcon}
          </span>
        )}
        <input
          type={type}
          name={name}
          disabled={disabled}
          value={value}
          defaultValue={defaultValue}
          onChange={onChange}
          onClick={onClick}
          accept={accept}
          max={max}
          min={min}
          maxLength={maxLength}
          minLength={minLength}
          pattern={pattern}
          required={required}
          placeholder={placeholder}
          checked={checked}
          inputMode={inputmode}
          className={`w-full py-3 placeholder:text-xl text-black ${leftIcon ? 'pl-12' : 'px-4'} ${
            rightIcon ? 'pr-12' : 'px-4'
          } border-b-2
            ${className || ''}
            ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : 'border-gray-300'}
            ${disabled ? 'cursor-not-allowed bg-gray-200' : ''}`}
        />
        {rightIcon && (
          <span className="absolute inset-y-0 right-3 flex items-center text-gray-500">
            {rightIcon}
          </span>
        )}
      </motion.div>
      {error && (
        <span className="text-red-500 text-sm mt-1">
          {errorMessage || 'This field is required'}
        </span>
      )}
    </div>
  );
};

Input.propTypes = {
  type: PropTypes.string.isRequired,
  name: PropTypes.string,
  label: PropTypes.string,
  disabled: PropTypes.bool,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  defaultValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func,
  onClick: PropTypes.func,
  accept: PropTypes.string,
  max: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  min: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  maxLength: PropTypes.number,
  minLength: PropTypes.number,
  pattern: PropTypes.string,
  required: PropTypes.bool,
  placeholder: PropTypes.string,
  className: PropTypes.string,
  error: PropTypes.bool,
  leftIcon: PropTypes.node,
  rightIcon: PropTypes.node,
  checked: PropTypes.bool,
  inputmode: PropTypes.string,
  errorMessage: PropTypes.string,
  multiple: PropTypes.bool
};

export default Input;
