import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import io from 'socket.io-client';
import { X, BarChart3, Video, VideoOff, Wifi, WifiOff, Clock, Users, Target, Trophy, Medal, Award, Star, ChevronDown, ChevronUp } from 'lucide-react';
import EngagementDashboard from './EngagementDashboard';

// API Configuration - Aligned with nginx proxy configuration
// /api/content/start_quiz -> http://195.35.21.98:8036
// /api/content/next_question -> http://195.35.21.98:8036
// /socketio2 -> http://195.35.21.98:8036
const FLASK_API_URL = 'https://sasthra.in';
const SOCKETIO_URL = 'https://sasthra.in';
const CAPTURE_INTERVAL_MS = 200;
const DURATION_PER_OPTION_S = 10; // Changed to match your reference (10 seconds)
const QUESTION_READING_TIME_S = 15;

// Enhanced Circular Option Display with Stunning Animations
const CircularOptionDisplay = React.memo(({ option, timeRemaining, totalTime, isActive }) => {
  // Early return for better performance
  if (!isActive || timeRemaining === null || timeRemaining === undefined) return null;

  // Static calculations to avoid unnecessary re-renders
  const radius = 120;
  const circumference = 2 * Math.PI * radius;

  // Ensure timeRemaining is valid
  const validTimeRemaining = Math.max(0, Math.min(timeRemaining, totalTime));
  const percentage = validTimeRemaining / totalTime;

  // Calculate progress for stroke animation
  const progress = percentage * circumference;
  const strokeDashoffset = circumference - progress;

  // Enhanced color scheme based on time remaining
  const getTimerColors = () => {
    if (percentage > 0.7) return {
      primary: '#10B981', // Emerald
      secondary: '#059669',
      glow: '#34D399',
      bg: 'from-emerald-400 to-green-500'
    };
    if (percentage > 0.4) return {
      primary: '#F59E0B', // Amber
      secondary: '#D97706',
      glow: '#FBBF24',
      bg: 'from-amber-400 to-orange-500'
    };
    return {
      primary: '#EF4444', // Red
      secondary: '#DC2626',
      glow: '#F87171',
      bg: 'from-red-400 to-pink-500'
    };
  };

  const colors = getTimerColors();
  const scale = 1 + (1 - percentage) * 0.08; // More noticeable scale change
  const rotation = (1 - percentage) * 360; // Full rotation as time decreases

  console.log('CircularOptionDisplay render:', { option, timeRemaining: validTimeRemaining, totalTime, percentage });

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none">
      {/* Enhanced animated background with particles */}
      <div className="absolute inset-0 bg-gradient-to-br from-black/30 via-purple-900/20 to-blue-900/30 backdrop-blur-md">
        {/* Floating particles */}
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="absolute w-3 h-3 rounded-full opacity-40 animate-float"
            style={{
              background: `linear-gradient(45deg, ${colors.primary}, ${colors.glow})`,
              top: `${10 + i * 8}%`,
              left: `${5 + i * 8}%`,
              animationDelay: `${i * 0.3}s`,
              animationDuration: `${3 + (i % 3)}s`,
              filter: `drop-shadow(0 0 8px ${colors.glow})`
            }}
          />
        ))}
      </div>

      {/* Main circular container with enhanced animations */}
      <div
        className="relative transform transition-all duration-300 ease-out"
        style={{
          transform: `scale(${scale}) rotate(${rotation * 0.1}deg)`,
          filter: `drop-shadow(0 0 30px ${colors.glow})`
        }}
      >
        {/* Outer glow rings */}
        <div
          className="absolute inset-0 w-80 h-80 rounded-full opacity-30 animate-pulse"
          style={{
            background: `conic-gradient(from 0deg, ${colors.primary}, ${colors.glow}, ${colors.secondary}, ${colors.primary})`,
            filter: 'blur(20px)'
          }}
        />

        {/* Secondary glow ring */}
        <div
          className="absolute inset-4 w-72 h-72 rounded-full opacity-20"
          style={{
            background: `radial-gradient(circle, ${colors.glow}40, transparent 70%)`,
            animation: 'spin 8s linear infinite reverse'
          }}
        />

        {/* Main circle container */}
        <div className="relative w-64 h-64 flex items-center justify-center">
          {/* Enhanced SVG Circle with multiple layers */}
          <svg
            className="absolute inset-0 transform -rotate-90 w-full h-full"
            viewBox="0 0 260 260"
            style={{ willChange: 'transform' }}
          >
            {/* Outer decorative ring */}
            <circle
              cx="130"
              cy="130"
              r="125"
              fill="none"
              stroke={colors.glow}
              strokeWidth="2"
              strokeDasharray="8,4"
              opacity="0.6"
              style={{
                animation: 'spin 6s linear infinite',
                filter: `drop-shadow(0 0 4px ${colors.glow})`
              }}
            />

            {/* Background track with gradient */}
            <circle
              cx="130"
              cy="130"
              r={radius}
              fill="none"
              stroke="rgba(255, 255, 255, 0.2)"
              strokeWidth="12"
            />

            {/* Animated progress circle with enhanced styling */}
            <circle
              cx="130"
              cy="130"
              r={radius}
              fill="none"
              stroke={colors.primary}
              strokeWidth="12"
              strokeLinecap="round"
              strokeDasharray={circumference}
              strokeDashoffset={strokeDashoffset}
              style={{
                transition: 'stroke-dashoffset 0.1s linear, stroke 0.3s ease',
                willChange: 'stroke-dashoffset',
                filter: `drop-shadow(0 0 12px ${colors.glow})`
              }}
            />

            {/* Inner decorative circle */}
            <circle
              cx="130"
              cy="130"
              r="100"
              fill="none"
              stroke={colors.secondary}
              strokeWidth="1"
              strokeDasharray="4,4"
              opacity="0.4"
              style={{
                animation: 'spin 4s linear infinite reverse'
              }}
            />
          </svg>

          {/* Enhanced center content */}
          <div
            className="relative z-10 flex flex-col items-center justify-center rounded-full w-48 h-48 shadow-2xl border-4"
            style={{
              background: `linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.85))`,
              borderColor: colors.glow,
              boxShadow: `0 0 40px ${colors.glow}40, inset 0 0 20px rgba(255,255,255,0.8)`
            }}
          >
            {/* Option letter with enhanced styling */}
            <div
              className="text-8xl font-black mb-2 transform transition-all duration-300"
              style={{
                background: `linear-gradient(135deg, ${colors.primary}, ${colors.secondary})`,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                transform: `scale(${1 + (1 - percentage) * 0.1}) rotate(${rotation * 0.05}deg)`,
                filter: `drop-shadow(0 0 8px ${colors.glow}60)`,
                textShadow: `0 0 20px ${colors.glow}`
              }}
            >
              {option}
            </div>

            {/* Enhanced countdown number */}
            <div
              className="text-4xl font-bold mb-3 transition-all duration-200"
              style={{
                color: colors.primary,
                transform: `scale(${1 + (1 - percentage) * 0.15})`,
                filter: `drop-shadow(0 2px 8px ${colors.primary}40)`,
                textShadow: `0 0 10px ${colors.glow}`
              }}
              key={validTimeRemaining}
            >
              {validTimeRemaining}s
            </div>

            {/* Enhanced progress indicators */}
            <div className="flex gap-1">
              {[...Array(Math.min(totalTime, 10))].map((_, i) => (
                <div
                  key={i}
                  className="w-2 h-2 rounded-full transition-all duration-200"
                  style={{
                    background: i < totalTime - validTimeRemaining
                      ? `linear-gradient(45deg, ${colors.primary}, ${colors.secondary})`
                      : 'rgba(255,255,255,0.4)',
                    transform: i < totalTime - validTimeRemaining ? 'scale(1.2)' : 'scale(1)',
                    boxShadow: i < totalTime - validTimeRemaining
                      ? `0 0 6px ${colors.glow}`
                      : 'none'
                  }}
                />
              ))}
            </div>

            {/* Pulse effect overlay */}
            <div
              className="absolute inset-0 rounded-full pointer-events-none"
              style={{
                background: `radial-gradient(circle, ${colors.glow}20, transparent 60%)`,
                animation: percentage < 0.3 ? 'pulse 1s ease-in-out infinite' : 'none'
              }}
            />
          </div>

          {/* Rotating outer decorative elements */}
          <div
            className="absolute inset-0 w-full h-full pointer-events-none"
            style={{ animation: 'spin 12s linear infinite' }}
          >
            {[...Array(8)].map((_, i) => (
              <div
                key={i}
                className="absolute w-2 h-2 rounded-full"
                style={{
                  background: colors.glow,
                  top: '10px',
                  left: '50%',
                  transformOrigin: '0 118px',
                  transform: `rotate(${i * 45}deg) translateX(-1px)`,
                  boxShadow: `0 0 8px ${colors.glow}`,
                  opacity: 0.6
                }}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
});

// Enhanced Reading Timer Component
const ReadingTimer = ({ timeRemaining, isActive }) => {
  if (!isActive) return null;

  return (
    <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-40">
      <div className="bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 text-white px-12 py-8 rounded-3xl shadow-2xl animate-pulse border-4 border-white/30 backdrop-blur-sm">
        <div className="text-center">
          <div className="text-3xl font-bold mb-3 flex items-center justify-center gap-3">
            <Clock className="animate-spin" size={32} />
            Reading Time
          </div>
          <div className="text-6xl font-black animate-bounce drop-shadow-lg">{timeRemaining}s</div>
          <div className="mt-3 text-lg opacity-90">Prepare for the question</div>
        </div>
      </div>
    </div>
  );
};

// Enhanced Hand Raise Animation Component
const HandRaiseNotification = ({ logs }) => {
  const [showNotification, setShowNotification] = useState(false);
  const [latestLog, setLatestLog] = useState(null);

  useEffect(() => {
    if (logs.length > 0) {
      const newest = logs[logs.length - 1];
      setLatestLog(newest);
      setShowNotification(true);

      const timer = setTimeout(() => {
        setShowNotification(false);
      }, 4000);

      return () => clearTimeout(timer);
    }
  }, [logs]);

  if (!showNotification || !latestLog) return null;

  return (
    <div className="fixed top-24 right-8 z-50 transform animate-slideInRight">
      <div className="bg-gradient-to-r from-green-400 via-emerald-400 to-teal-400 text-white px-8 py-6 rounded-2xl shadow-2xl border-4 border-white/30 backdrop-blur-sm transform hover:scale-105 transition-all duration-300">
        <div className="flex items-center gap-4">
          <div className="text-4xl animate-bounce">🙋‍♂️</div>
          <div>
            <div className="font-bold text-xl drop-shadow-sm">{latestLog.student_name}</div>
            <div className="text-sm opacity-90 bg-white/20 px-3 py-1 rounded-full mt-1">
              Option {latestLog.option.toUpperCase()} • {latestLog.responseTime}s
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// F1-Style Animated Leaderboard Component
const AnimatedLeaderboard = ({ finalResults, onClose }) => {
  const [showAllParticipants, setShowAllParticipants] = useState(false);
  const [animationPhase, setAnimationPhase] = useState('entering'); // entering, displaying, exiting

  const topStudents = useMemo(() => {
    if (!finalResults?.overall_summary?.top_engaged_students) return [];
    return finalResults.overall_summary.top_engaged_students.slice(0, 5);
  }, [finalResults]);

  const allParticipants = useMemo(() => {
    if (!finalResults?.overall_summary?.top_engaged_students) return [];
    return finalResults.overall_summary.top_engaged_students;
  }, [finalResults]);

  const getRankIcon = useCallback((rank) => {
    switch (rank) {
      case 1: return <Trophy className="text-yellow-500" size={32} />;
      case 2: return <Medal className="text-gray-400" size={28} />;
      case 3: return <Award className="text-amber-600" size={24} />;
      default: return <Star className="text-blue-500" size={20} />;
    }
  }, []);

  const getRankColors = useCallback((rank) => {
    switch (rank) {
      case 1: return {
        bg: 'from-yellow-400 via-yellow-500 to-yellow-600',
        border: 'border-yellow-400',
        text: 'text-yellow-900',
        shadow: 'shadow-yellow-300/50'
      };
      case 2: return {
        bg: 'from-gray-300 via-gray-400 to-gray-500',
        border: 'border-gray-400',
        text: 'text-gray-900',
        shadow: 'shadow-gray-300/50'
      };
      case 3: return {
        bg: 'from-amber-500 via-amber-600 to-amber-700',
        border: 'border-amber-500',
        text: 'text-amber-900',
        shadow: 'shadow-amber-300/50'
      };
      default: return {
        bg: 'from-blue-400 via-blue-500 to-blue-600',
        border: 'border-blue-400',
        text: 'text-blue-900',
        shadow: 'shadow-blue-300/50'
      };
    }
  }, []);

  const getPerformanceEmoji = useCallback((accuracy) => {
    if (accuracy >= 80) return '🏆';
    if (accuracy >= 60) return '🥈';
    if (accuracy >= 40) return '🥉';
    if (accuracy >= 20) return '📈';
    return '💪';
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimationPhase('displaying');
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 z-50 flex items-center justify-center p-4 overflow-hidden">
      {/* Animated background particles */}
      <div className="absolute inset-0">
        {[...Array(50)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-white rounded-full opacity-20 animate-float"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              animationDelay: `${i * 0.1}s`,
              animationDuration: `${3 + (i % 3)}s`
            }}
          />
        ))}
      </div>

      {/* Main container */}
      <div className="bg-white/95 backdrop-blur-xl rounded-3xl border border-white/20 w-full max-w-6xl max-h-[95vh] flex flex-col shadow-2xl">
        {/* Header */}
        <div className="flex justify-between items-center p-8 border-b border-gray-200/50 bg-gradient-to-r from-purple-100/50 via-blue-100/50 to-indigo-100/50">
          <div className="flex items-center gap-6">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 via-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center shadow-lg">
              <Trophy className="text-white" size={32} />
            </div>
            <div>
              <h2 className="text-4xl font-black bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent">
                🏁 Quiz Results
              </h2>
              <p className="text-gray-600 text-lg font-medium">Final Leaderboard</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-red-500 transition-all duration-300 p-3 rounded-full hover:bg-red-50 hover:scale-110 transform"
          >
            <X size={28} />
          </button>
        </div>

        {/* Scrollable content */}
        <div className="flex-1 overflow-y-auto p-8">
          {/* Quiz Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-gradient-to-r from-green-100 to-emerald-100 p-6 rounded-2xl border-2 border-green-300 shadow-xl">
              <div className="text-center">
                <div className="text-3xl font-black text-green-800">{finalResults?.overall_summary?.engagement_summary_stats?.active_students || 0}</div>
                <div className="text-green-700 font-bold">Active Students</div>
              </div>
            </div>
            <div className="bg-gradient-to-r from-blue-100 to-cyan-100 p-6 rounded-2xl border-2 border-blue-300 shadow-xl">
              <div className="text-center">
                <div className="text-3xl font-black text-blue-800">{finalResults?.overall_summary?.total_questions_in_quiz || 0}</div>
                <div className="text-blue-700 font-bold">Total Questions</div>
              </div>
            </div>
            <div className="bg-gradient-to-r from-purple-100 to-pink-100 p-6 rounded-2xl border-2 border-purple-300 shadow-xl">
              <div className="text-center">
                <div className="text-3xl font-black text-purple-800">{finalResults?.overall_summary?.engagement_summary_stats?.total_participations || 0}</div>
                <div className="text-purple-700 font-bold">Total Responses</div>
              </div>
            </div>
            <div className="bg-gradient-to-r from-orange-100 to-red-100 p-6 rounded-2xl border-2 border-orange-300 shadow-xl">
              <div className="text-center">
                <div className="text-3xl font-black text-orange-800">{Math.round(finalResults?.overall_summary?.engagement_summary_stats?.average_frequency || 0)}x</div>
                <div className="text-orange-700 font-bold">Avg Frequency</div>
              </div>
            </div>
          </div>

          {/* Top 5 Leaderboard */}
          <div className="mb-8">
            <h3 className="text-3xl font-black mb-6 text-gray-800 flex items-center gap-3">
              <Trophy className="text-yellow-500" size={32} />
              Top Performers
            </h3>
            <div className="space-y-4">
              {topStudents.map((student, index) => {
                const colors = getRankColors(student.rank);
                return (
                  <div
                    key={student.name + index}
                    className={`transform transition-all duration-1000 ease-out ${
                      animationPhase === 'entering'
                        ? 'translate-x-full opacity-0'
                        : 'translate-x-0 opacity-100'
                    }`}
                    style={{
                      transitionDelay: `${index * 200}ms`,
                      animation: animationPhase === 'displaying' ? `slideInFromRight 0.8s ease-out ${index * 0.2}s both` : ''
                    }}
                  >
                    <div className={`bg-gradient-to-r ${colors.bg} p-6 rounded-2xl border-4 ${colors.border} shadow-2xl ${colors.shadow} hover:scale-105 transition-transform duration-300`}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-6">
                          {/* Rank Badge */}
                          <div className="relative">
                            <div className={`w-16 h-16 rounded-full bg-white/90 flex items-center justify-center shadow-lg ${colors.text}`}>
                              {getRankIcon(student.rank)}
                            </div>
                            <div className="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center text-sm font-black text-gray-800 shadow-lg">
                              #{student.rank}
                            </div>
                          </div>

                          {/* Student Info */}
                          <div className="text-white">
                            <div className="text-2xl font-black mb-1 flex items-center gap-2">
                              {getPerformanceEmoji(student.accuracy_percentage)} {student.name}
                            </div>
                            <div className="text-lg opacity-90 font-bold">
                              Score: {student.score} • Accuracy: {student.accuracy_percentage}%
                            </div>
                            <div className="text-sm opacity-80 font-medium">
                              {student.questions_attempted} questions • Avg: {student.avg_response_time_seconds}s
                            </div>
                          </div>
                        </div>

                        {/* Performance Badge */}
                        <div className="text-right">
                          <div className="bg-white/20 px-4 py-2 rounded-full text-white font-bold text-lg">
                            {student.performance_message}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* All Participants Toggle */}
          {allParticipants.length > 5 && (
            <div className="text-center mb-6">
              <button
                onClick={() => setShowAllParticipants(!showAllParticipants)}
                className="px-8 py-4 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 flex items-center gap-3 mx-auto transform hover:scale-105"
              >
                {showAllParticipants ? <ChevronUp size={24} /> : <ChevronDown size={24} />}
                {showAllParticipants ? 'Hide' : 'Show'} All Participants ({allParticipants.length})
              </button>
            </div>
          )}

          {/* All Participants List */}
          {showAllParticipants && (
            <div className="bg-gradient-to-r from-white/80 to-gray-50/80 rounded-2xl p-6 border-2 border-gray-200 backdrop-blur-sm shadow-xl">
              <h4 className="text-2xl font-black mb-4 text-gray-800 flex items-center gap-3">
                <Users className="text-blue-500" size={24} />
                All Participants
              </h4>
              <div className="grid gap-3 max-h-96 overflow-y-auto">
                {allParticipants.map((student, index) => (
                  <div
                    key={student.name + index}
                    className="bg-white/80 p-4 rounded-xl border border-gray-200 hover:bg-gray-50/80 transition-all duration-300 transform hover:scale-102"
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                          #{student.rank}
                        </div>
                        <div>
                          <div className="font-bold text-gray-800">{student.name}</div>
                          <div className="text-sm text-gray-600">
                            {student.questions_attempted} questions • {student.avg_response_time_seconds}s avg
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-gray-800">Score: {student.score}</div>
                        <div className="text-sm text-gray-600">{student.accuracy_percentage}% accuracy</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Enhanced animations */}
      <style jsx>{`
        @keyframes slideInFromRight {
          0% {
            transform: translateX(100%) scale(0.8);
            opacity: 0;
          }
          50% {
            transform: translateX(-10%) scale(1.05);
            opacity: 0.8;
          }
          100% {
            transform: translateX(0) scale(1);
            opacity: 1;
          }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
          }
          50% {
            transform: translateY(-20px) rotate(180deg);
          }
        }

        .animate-float {
          animation: float 4s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
};

const LiveQuiz = ({ quizId: inputQuizId, onClose }) => {
  const [quizId, setQuizId] = useState(inputQuizId);
  const [questionData, setQuestionData] = useState(null);
  const [quizStatus, setQuizStatus] = useState('');
  const [finalResults, setFinalResults] = useState(null);
  const [liveFeedLogs, setLiveFeedLogs] = useState([]);
  const [currentQuestionStartTime, setCurrentQuestionStartTime] = useState(null);
  const [socketStatus, setSocketStatus] = useState('Disconnected');
  const [error, setError] = useState(null);
  const [currentOptionTimer, setCurrentOptionTimer] = useState(null);
  const [currentOption, setCurrentOption] = useState('');
  const [questionTimer, setQuestionTimer] = useState(null);
  const [isReadingQuestion, setIsReadingQuestion] = useState(false);


  const [showDashboard, setShowDashboard] = useState(false);
  const [showLeaderboard, setShowLeaderboard] = useState(false);
  const [questionCount, setQuestionCount] = useState(0);
  const [cameraStatus, setCameraStatus] = useState('Not initialized');

  // Manual quiz ID input states
  const [showManualInput, setShowManualInput] = useState(!inputQuizId);
  const [manualQuizId, setManualQuizId] = useState('');

  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const socketRef = useRef(null);
  const videoStreamRef = useRef(null);
  const timerIntervalRef = useRef(null);

  // Initialize camera when quizId is available (like ContentQuiz.jsx)
  useEffect(() => {
    if (quizId && videoRef.current) {
      console.log('🎯 QuizId changed, initializing camera:', quizId);
      initCamera();
    }
  }, [quizId]);

  useEffect(() => {
    return () => {
      stopCamera();
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
      }
    };
  }, []);

  const startQuiz = async () => {
    if (!inputQuizId) {
      setError('Missing Quiz ID');
      return;
    }

    try {
      setQuizStatus('Starting quiz...');

      const response = await fetch(`${FLASK_API_URL}/api/content/start_quiz`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ quiz_id: inputQuizId })
      });

      const data = await response.json();
      if (!response.ok || data.error)
        throw new Error(data.error || `Server error ${response.status}`);

      setQuestionData(data);
      setQuizId(data.quiz_id);
      setQuizStatus('Ready to start capture for this question.');
      setQuestionCount(1);

      // Initialize camera immediately after setting quiz data
      console.log('🎯 Starting quiz, initializing camera immediately');
      await initCamera();

      setupSocketIO();
    } catch (error) {
      setError(`Error starting quiz: ${error.message}`);
    }
  };

  const handleNextQuestion = async () => {
    if (!quizId) return;

    try {
      await startCaptureCycle();
      setQuizStatus('Processing results and fetching next question...');

      const res = await fetch(`${FLASK_API_URL}/api/content/next_question`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ quiz_id: quizId, process_selector_id: inputQuizId })
      });

      const data = await res.json();

      if (data.overall_summary) {
        setFinalResults(data);
        stopCamera();
        socketRef.current?.disconnect();
        setQuizStatus('Quiz completed! View engagement dashboard for detailed analytics.');
        if (questionCount >= 5) {
          setShowDashboard(true);
        }
      } else if (data.question) {
        setQuestionData(data);
        setQuizStatus('Ready to start capture for this question.');
        setQuestionCount((prev) => prev + 1);
      }
    } catch (error) {
      setError(`Error during quiz progression: ${error.message}`);
      setQuizStatus(`Error: ${error.message}`);
    }
  };

  const startCaptureCycle = async () => {
    setLiveFeedLogs([]);

    setIsReadingQuestion(true);
    setQuestionTimer(QUESTION_READING_TIME_S);
    setCurrentQuestionStartTime(Date.now());

    timerIntervalRef.current = setInterval(() => {
      setQuestionTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timerIntervalRef.current);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    await new Promise((resolve) => setTimeout(resolve, QUESTION_READING_TIME_S * 1000));

    setIsReadingQuestion(false);
    setQuestionTimer(null);

    const options = ['a', 'b', 'c', 'd'];

    for (let i = 0; i < options.length; i++) {
      setQuizStatus(
        `Capturing for option ${options[i].toUpperCase()}... (${i + 1}/${options.length})`
      );
      await processSingleOption(options[i]);
    }

    setQuizStatus('Capture complete for this question. Results sent.');

    setCurrentOptionTimer(null);
    setCurrentOption('');
  };

  const processSingleOption = (optionChar) => {
    return new Promise((resolve) => {
      // Clear any existing timers first
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
        timerIntervalRef.current = null;
      }

      const startTime = Date.now();
      let frameCount = 0;

      console.log(`🎯 Starting option ${optionChar.toUpperCase()} timer for ${DURATION_PER_OPTION_S}s`);

      setCurrentOption(optionChar.toUpperCase());
      setCurrentOptionTimer(DURATION_PER_OPTION_S);

      // Create a more reliable timer with better precision
      const updateTimer = () => {
        try {
          const elapsed = (Date.now() - startTime) / 1000;
          const remaining = Math.max(0, DURATION_PER_OPTION_S - elapsed);

          console.log(`⏱️ Timer update: elapsed=${elapsed.toFixed(1)}s, remaining=${remaining.toFixed(1)}s`);

          const displayTime = Math.ceil(remaining);
          setCurrentOptionTimer(displayTime);

          if (remaining > 0.1) { // Small buffer to prevent timing issues
            timerIntervalRef.current = setTimeout(updateTimer, 100);
          } else {
            console.log(`⏰ Timer completed for option ${optionChar.toUpperCase()}`);
            setCurrentOptionTimer(0);
          }
        } catch (error) {
          console.error('Timer update error:', error);
          setCurrentOptionTimer(0);
        }
      };

      // Start the timer immediately
      updateTimer();

      const intervalId = setInterval(() => {
        const responseTime = (Date.now() - startTime) / 1000;
        captureAndSendFrame(optionChar, responseTime);
        frameCount++;
      }, CAPTURE_INTERVAL_MS);

      setTimeout(() => {
        console.log(`✅ Option ${optionChar.toUpperCase()} completed`);
        clearInterval(intervalId);
        if (timerIntervalRef.current) {
          clearTimeout(timerIntervalRef.current);
          timerIntervalRef.current = null;
        }
        setCurrentOptionTimer(0);
        resolve();
      }, DURATION_PER_OPTION_S * 1000);
    });
  };

  const captureAndSendFrame = (optionChar, responseTime) => {
    const video = videoRef.current;
    const canvas = canvasRef.current;

    if (!video || !canvas) {
      console.warn('⚠️ No video or canvas element available');
      return;
    }

    if (!videoStreamRef.current) {
      console.warn('⚠️ No video stream available');
      return;
    }

    if (!socketRef.current?.connected) {
      console.warn('⚠️ Socket not connected');
      return;
    }

    const ctx = canvas.getContext('2d');
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    const frameData = canvas.toDataURL('image/jpeg', 0.6);

    console.log(
      `📸 Sending frame: quiz=${quizId}, option=${optionChar}, time=${responseTime.toFixed(2)}s`
    );

    socketRef.current.emit('process_frame', {
      quiz_id: quizId,
      frame: frameData,
      option_char: optionChar,
      response_time_seconds: responseTime
    });
  };

  const setupSocketIO = () => {
    if (socketRef.current) {
      socketRef.current.disconnect();
    }

    socketRef.current = io(SOCKETIO_URL, {
      path: '/socketio2/socket.io',
      transports: ['websocket', 'polling'],
      upgrade: true,
      rememberUpgrade: true,
      timeout: 20000,
      forceNew: true,
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      maxReconnectionAttempts: 5
    });

    socketRef.current.on('connect', () => {
      setSocketStatus('Connected');
    });

    socketRef.current.on('disconnect', (reason) => {
      console.log('❌ Disconnected from Socket.IO server. Reason:', reason);
      setSocketStatus('Disconnected');
    });

    socketRef.current.on('connect_error', (error) => {
      console.error('❌ Socket.IO connection error:', error);
      setSocketStatus('Connection Error');
    });

    socketRef.current.on('reconnect', (attemptNumber) => {
      console.log('🔄 Reconnected to Socket.IO server. Attempt:', attemptNumber);
      setSocketStatus('Reconnected');
    });

    socketRef.current.on('reconnect_error', (error) => {
      console.error('❌ Socket.IO reconnection error:', error);
    });

    socketRef.current.on('reconnect_failed', () => {
      console.error('❌ Socket.IO reconnection failed');
      setSocketStatus('Reconnection Failed');
    });

    socketRef.current.on('hand_raised', (data) => {
      console.log('🙋 Hand raised event received:', data);
      setLiveFeedLogs((logs) => {
        const newLog = {
          ...data,
          responseTime: currentQuestionStartTime
            ? Math.round((new Date(data.detection_timestamp) - currentQuestionStartTime) / 1000)
            : 0
        };
        return [...logs, newLog];
      });
    });

    socketRef.current.onAny((eventName, ...args) => {
      console.log(`📡 Socket event received: ${eventName}`, args);
    });
  };

  const initCamera = async () => {
    if (videoStreamRef.current) return;

    try {
      console.log('🎥 Requesting camera access...');
      setCameraStatus('Requesting access...');

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoStreamRef.current = stream;
        console.log('✅ Camera initialized successfully');
        setCameraStatus('Active');
      }
    } catch (error) {
      console.error('❌ Camera error:', error);
      setCameraStatus('Error');
      setError(`Camera error: ${error.message}`);
    }
  };

  const stopCamera = () => {
    if (videoStreamRef.current) {
      videoStreamRef.current.getTracks().forEach((track) => track.stop());
      videoStreamRef.current = null;
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
    setCameraStatus('Not initialized');
  };

  const testSocketConnection = () => {
    if (socketRef.current) {
      socketRef.current.emit('test_connection', { message: 'Hello from client' });
    }
  };

  const handleCloseDashboard = () => {
    setShowDashboard(false);
  };

  const handleManualQuizStart = async () => {
    if (manualQuizId.trim()) {
      setError(null); // Clear any previous errors
      setShowManualInput(false);
      setQuizId(manualQuizId.trim()); // Set the quiz ID first
      // Start quiz with manual ID
      await startQuizWithId(manualQuizId.trim());
    } else {
      setError('Please enter a valid Quiz ID');
    }
  };

  const startQuizWithId = async (quizIdToUse) => {
    try {
      setQuizStatus('Starting quiz...');

      const response = await fetch(`${FLASK_API_URL}/api/content/start_quiz`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ quiz_id: quizIdToUse })
      });

      const data = await response.json();
      if (!response.ok || data.error)
        throw new Error(data.error || `Server error ${response.status}`);

      setQuestionData(data);
      setQuizId(data.quiz_id); // This will trigger camera initialization via useEffect
      setQuizStatus('Ready to start capture for this question.');
      setQuestionCount(1);
      setupSocketIO();
    } catch (error) {
      setError(`Error starting quiz: ${error.message}`);
    }
  };

  useEffect(() => {
    if (inputQuizId) {
      startQuiz();
    }
  }, [inputQuizId]);

  if (showDashboard && quizId) {
    return <EngagementDashboard quizId={quizId} onClose={handleCloseDashboard} />;
  }

  if (showLeaderboard && finalResults) {
    return <AnimatedLeaderboard finalResults={finalResults} onClose={() => setShowLeaderboard(false)} />;
  }

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 z-50 flex items-center justify-center p-4">
      {/* Minimal background for better performance */}

      {/* Circular Option Display */}
      <CircularOptionDisplay
        option={currentOption}
        timeRemaining={currentOptionTimer}
        totalTime={DURATION_PER_OPTION_S}
        isActive={!!currentOption && currentOptionTimer !== null}
      />

      {/* Reading Timer */}
      <ReadingTimer timeRemaining={questionTimer} isActive={isReadingQuestion} />

      {/* Hand Raise Notifications */}
      <HandRaiseNotification logs={liveFeedLogs} />

      <div className="bg-white/95 backdrop-blur-xl rounded-3xl border border-white/20 w-full max-w-7xl max-h-[95vh] flex flex-col shadow-2xl hw-accelerate">
        {/* Header - Fixed */}
        <div className="flex justify-between items-center p-8 border-b border-gray-200/50 flex-shrink-0 bg-gradient-to-r from-blue-100/50 via-purple-100/50 to-pink-100/50">
          <div className="flex items-center gap-6">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg transform hover:scale-110 transition-all duration-300">
              <Target className="text-white" size={32} />
            </div>
            <div>
              <h2 className="text-3xl font-black bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                Live Quiz Experience
              </h2>
              <p className="text-gray-600 text-lg font-medium">Real-time engagement monitoring</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-red-500 transition-all duration-300 p-3 rounded-full hover:bg-red-50 hover:scale-110 transform"
          >
            <X size={28} />
          </button>
        </div>

        {/* Optimized Scrollable Content */}
        <div
          className="flex-1 overflow-y-auto p-8 space-y-8 scroll-container"
          style={{
            scrollBehavior: 'smooth',
            willChange: 'scroll-position'
          }}
        >
          {/* Optimized Status Bar */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Socket Status */}
            <div
              className={`p-6 rounded-2xl border-2 transition-colors duration-300 ${
                socketStatus === 'Connected'
                  ? 'bg-green-50 border-green-300 text-green-800'
                  : 'bg-red-50 border-red-300 text-red-800'
              } shadow-lg`}
            >
              <div className="flex items-center gap-4">
                <div
                  className={`p-3 rounded-xl ${socketStatus === 'Connected' ? 'bg-green-200' : 'bg-red-200'}`}
                >
                  {socketStatus === 'Connected' ? <Wifi size={24} /> : <WifiOff size={24} />}
                </div>
                <div>
                  <div className="font-bold text-lg">Socket Status</div>
                  <div className="text-sm opacity-80 font-medium">{socketStatus}</div>
                </div>
              </div>
            </div>

            {/* Camera Status */}
            <div
              className={`p-6 rounded-2xl border-2 transition-colors duration-300 ${
                cameraStatus === 'Active'
                  ? 'bg-blue-50 border-blue-300 text-blue-800'
                  : 'bg-yellow-50 border-yellow-300 text-yellow-800'
              } shadow-lg`}
            >
              <div className="flex items-center gap-4">
                <div
                  className={`p-3 rounded-xl ${cameraStatus === 'Active' ? 'bg-blue-200' : 'bg-yellow-200'}`}
                >
                  {cameraStatus === 'Active' ? <Video size={24} /> : <VideoOff size={24} />}
                </div>
                <div>
                  <div className="font-bold text-lg">Camera Status</div>
                  <div className="text-sm opacity-80 font-medium">{cameraStatus}</div>
                </div>
              </div>
            </div>

            {/* Quiz Progress */}
            <div className="p-6 rounded-2xl border-2 bg-purple-50 border-purple-300 text-purple-800 shadow-lg transition-colors duration-300">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 rounded-xl bg-purple-500 flex items-center justify-center text-white font-black text-xl shadow-lg">
                  {questionCount}
                </div>
                <div>
                  <div className="font-bold text-lg">Question Progress</div>
                  <div className="text-sm opacity-80 font-medium">Question {questionCount}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Test Connection Button */}
          {socketRef.current && (
            <button
              onClick={testSocketConnection}
              className="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white rounded-2xl font-bold transition-all duration-300 shadow-xl transform hover:scale-105"
            >
              Test Socket Connection
            </button>
          )}

          {/* Error Display */}
          {error && (
            <div className="text-center text-red-600 p-8 bg-gradient-to-r from-red-50 to-pink-50 rounded-2xl border-2 border-red-200 animate-pulse shadow-xl">
              <div className="text-6xl mb-4">⚠️</div>
              <p className="text-xl font-bold">{error}</p>
            </div>
          )}

          {/* Manual Quiz ID Input */}
          {showManualInput && (
            <div className="bg-gradient-to-r from-white/80 to-blue-50/80 rounded-2xl p-8 border-2 border-blue-200 backdrop-blur-sm shadow-xl">
              <h3 className="text-3xl font-black text-gray-800 mb-6 flex items-center gap-4">
                <span className="text-4xl">🎯</span>
                Enter Quiz ID
              </h3>
              <div className="flex gap-6 items-end">
                <div className="flex-1">
                  <label className="block text-lg font-bold text-gray-700 mb-3">Quiz ID</label>
                  <input
                    type="text"
                    value={manualQuizId}
                    onChange={(e) => setManualQuizId(e.target.value)}
                    placeholder="Enter quiz ID (e.g., 686b5c81d51a7d6958c15fdc)"
                    className="w-full px-6 py-4 bg-white/80 border-2 border-gray-300 rounded-2xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-purple-300 focus:border-purple-400 transition-all backdrop-blur-sm text-lg font-medium shadow-lg"
                    onKeyDown={(e) => e.key === 'Enter' && handleManualQuizStart()}
                  />
                </div>
                <button
                  onClick={handleManualQuizStart}
                  disabled={!manualQuizId.trim()}
                  className="px-10 py-4 bg-gradient-to-r from-purple-500 via-blue-500 to-teal-500 hover:from-purple-600 hover:via-blue-600 hover:to-teal-600 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 disabled:cursor-not-allowed transform hover:scale-105 text-lg"
                >
                  Start Quiz
                </button>
              </div>
              <p className="text-gray-600 text-lg mt-4 font-medium">
                Enter the Quiz ID provided by your teacher to start the quiz.
              </p>
            </div>
          )}

          {/* Show option to switch to manual input if quiz started automatically */}
          {!showManualInput && questionData && (
            <div>
              <button
                onClick={() => {
                  setShowManualInput(true);
                  setQuestionData(null);
                  setQuizId(null);
                  setError(null);
                }}
                className="px-6 py-3 bg-gradient-to-r from-gray-400 to-gray-500 hover:from-gray-500 hover:to-gray-600 text-white rounded-xl text-lg font-medium transition-all duration-300 backdrop-blur-sm shadow-lg transform hover:scale-105"
              >
                Enter Different Quiz ID
              </button>
            </div>
          )}

          {/* Quiz Content */}
          {questionData && (
            <div className="text-gray-800 space-y-8">
              {/* Status Bar */}
              <div className="status-bar p-6 bg-gradient-to-r from-white/80 to-blue-50/80 rounded-2xl border-2 border-blue-200 backdrop-blur-sm shadow-xl">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-lg">
                  <div className="font-bold">
                    <strong>Quiz ID:</strong> <span className="text-blue-600">{quizId}</span>
                  </div>
                  <div className="font-bold">
                    <strong>Status:</strong> <span className="text-purple-600">{quizStatus}</span>
                  </div>
                </div>
              </div>

              {/* Video and Live Feed Section */}
              <div className="flex flex-col lg:flex-row gap-8">
                {/* Video Section */}
                <div className="flex-1">
                  <div className="relative rounded-2xl overflow-hidden border-4 border-white/50 bg-gradient-to-br from-gray-100 to-gray-200 backdrop-blur-sm shadow-2xl">
                    <video
                      ref={videoRef}
                      autoPlay
                      playsInline
                      muted
                      className="w-full max-w-2xl rounded-2xl"
                    />
                    <div
                      className={`absolute top-6 left-6 px-4 py-3 rounded-xl text-lg font-bold backdrop-blur-sm shadow-lg ${
                        cameraStatus === 'Active'
                          ? 'bg-green-500/90 text-white'
                          : cameraStatus === 'Error'
                            ? 'bg-red-500/90 text-white'
                            : 'bg-yellow-500/90 text-white'
                      }`}
                    >
                      📹 Camera: {cameraStatus}
                    </div>
                  </div>
                  <canvas ref={canvasRef} className="hidden" />
                </div>

                {/* Live Feed Logs */}
                {liveFeedLogs.length > 0 && (
                  <div className="w-full lg:w-80 bg-gradient-to-b from-white/90 to-blue-50/90 rounded-2xl p-6 border-2 border-blue-200 max-h-96 overflow-y-auto backdrop-blur-sm shadow-xl">
                    <h3 className="text-xl font-black mb-4 text-gray-800 flex items-center gap-3">
                      <Users className="text-blue-500" size={24} />
                      Live Hand Raises ({liveFeedLogs.length})
                    </h3>
                    <div className="space-y-3">
                      {liveFeedLogs
                        .sort(
                          (a, b) =>
                            new Date(a.detection_timestamp) - new Date(b.detection_timestamp)
                        )
                        .map((log, idx) => (
                          <div
                            key={idx}
                            className="bg-gradient-to-r from-white/80 to-blue-50/80 p-4 rounded-xl text-sm backdrop-blur-sm transform hover:scale-105 transition-all duration-300 shadow-lg border border-blue-200"
                          >
                            <div className="flex justify-between items-start mb-2">
                              <span className="font-bold text-gray-800 text-lg">
                                #{idx + 1} {log.student_name}
                              </span>
                              <span className="text-sm text-gray-600 bg-blue-100 px-3 py-1 rounded-full font-medium">
                                {log.responseTime}s
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-blue-600 font-bold text-lg">
                                Option {log.option.toUpperCase()}
                              </span>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Question Section */}
              <div className="space-y-6">
                <h2 className="text-3xl font-black bg-gradient-to-r from-purple-600 via-blue-600 to-teal-600 bg-clip-text text-transparent">
                  {questionData.sub_topic_name}
                </h2>

                {/* Question Text */}
                <div className="bg-gradient-to-r from-white/80 to-purple-50/80 p-8 rounded-2xl border-2 border-purple-200 backdrop-blur-sm shadow-xl">
                  <p className="text-2xl text-gray-800 leading-relaxed font-medium">
                    {questionData.question_number}. {questionData.question}
                  </p>
                </div>

                {/* Question Image */}
                {questionData.question_image && questionData.question_image.trim() && (
                  <div className="bg-gradient-to-r from-white/80 to-blue-50/80 p-6 rounded-2xl border-2 border-blue-200 backdrop-blur-sm shadow-xl">
                    <img
                      src={questionData.question_image.trim()}
                      alt="Question Image"
                      className="max-w-full h-auto rounded-xl shadow-lg border-2 border-white/50"
                      onError={(e) => {
                        console.error('Failed to load question image:', e.target.src);
                        // Try base URL if signed URL fails
                        if (e.target.src.includes('?X-Amz-')) {
                          const baseUrl = e.target.src.split('?X-Amz-')[0];
                          console.log('Trying base URL:', baseUrl);
                          e.target.src = baseUrl;
                        } else {
                          // Hide the image container if loading fails completely
                          e.target.style.display = 'none';
                        }
                      }}
                      onLoad={() => {
                        console.log(
                          'Question image loaded successfully:',
                          questionData.question_image
                        );
                      }}
                    />
                  </div>
                )}

                {/* Optimized Options */}
                <div className="grid gap-4">
                  {questionData.options.map((opt, idx) => {
                    const optionLetter = String.fromCharCode(97 + idx);
                    const isCurrentOption = currentOption.toLowerCase() === optionLetter;

                    return (
                      <div key={`${idx}-${optionLetter}`} className="relative">
                        <div
                          className={`p-6 rounded-2xl border-2 transition-colors duration-300 ${
                            isCurrentOption
                              ? 'bg-green-100 border-green-400 shadow-lg scale-105'
                              : 'bg-white border-gray-300 hover:bg-gray-50 shadow-md'
                          }`}
                          style={{
                            transform: isCurrentOption ? 'scale(1.02)' : 'scale(1)',
                            transition: 'all 0.3s ease',
                            willChange: isCurrentOption ? 'transform' : 'auto'
                          }}
                        >
                          <span className="font-bold text-gray-800 text-xl">
                            {optionLetter.toUpperCase()}. {opt}
                          </span>
                        </div>
                        {isCurrentOption && currentOptionTimer !== null && (
                          <div
                            className="absolute top-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-full text-lg font-bold shadow-lg"
                            key={currentOptionTimer} // Force re-render on timer change
                          >
                            {currentOptionTimer}s
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Final Results - Show Animated Leaderboard */}
          {finalResults && (
            <div className="bg-gradient-to-r from-white/80 to-green-50/80 rounded-2xl p-8 border-2 border-green-200 backdrop-blur-sm shadow-xl">
              <h3 className="text-2xl font-black mb-6 text-gray-800 flex items-center gap-3">
                <span className="text-3xl">🎉</span>
                Quiz Completed!
              </h3>
              <div className="text-center">
                <button
                  onClick={() => setShowLeaderboard(true)}
                  className="px-8 py-4 bg-gradient-to-r from-purple-500 via-blue-500 to-indigo-500 hover:from-purple-600 hover:via-blue-600 hover:to-indigo-600 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 flex items-center gap-3 mx-auto transform hover:scale-105"
                >
                  <Trophy size={24} />
                  View Animated Results
                </button>
                <p className="text-gray-600 mt-4 text-lg">
                  See the F1-style leaderboard with all participant rankings!
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Footer with Action Buttons - Fixed at bottom */}
        {questionData && (
          <div className="border-t border-gray-200/50 p-8 flex-shrink-0 bg-gradient-to-r from-white/80 to-blue-50/80 backdrop-blur-sm">
            <div className="flex flex-col sm:flex-row gap-6 justify-between items-center">
              <div className="text-lg text-gray-700 flex items-center gap-4 font-medium">
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-black">
                  {questionCount}
                </div>
                Question {questionCount} • Quiz ID:{' '}
                <span className="text-blue-600 font-bold">{quizId}</span>
              </div>
              <div className="flex gap-4">
                {finalResults && questionCount >= 5 && (
                  <button
                    onClick={() => setShowDashboard(true)}
                    className="px-8 py-4 bg-gradient-to-r from-blue-500 via-cyan-500 to-teal-500 hover:from-blue-600 hover:via-cyan-600 hover:to-teal-600 text-white rounded-2xl font-bold shadow-xl transition-all duration-300 flex items-center gap-3 transform hover:scale-105"
                  >
                    <BarChart3 size={24} />
                    View Dashboard
                  </button>
                )}
                <button
                  onClick={handleNextQuestion}
                  className="px-10 py-4 bg-gradient-to-r from-green-500 via-emerald-500 to-teal-500 hover:from-green-600 hover:via-emerald-600 hover:to-teal-600 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-2xl font-black shadow-xl transition-all duration-300 disabled:cursor-not-allowed transform hover:scale-105 disabled:transform-none text-lg"
                  disabled={
                    quizStatus.includes('Capturing') ||
                    quizStatus.includes('Processing') ||
                    isReadingQuestion
                  }
                >
                  {finalResults ? 'Quiz Complete' : 'Start Capture & Next Question'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced CSS with stunning animations */}
      <style jsx>{`
        /* Hardware acceleration for smooth scrolling */
        * {
          -webkit-overflow-scrolling: touch;
        }

        .animate-slideInRight {
          animation: slideInRight 0.6s ease-out;
        }

        .animate-float {
          animation: float 4s ease-in-out infinite;
          will-change: transform;
        }

        @keyframes slideInRight {
          from {
            transform: translate3d(100%, 0, 0);
            opacity: 0;
          }
          to {
            transform: translate3d(0, 0, 0);
            opacity: 1;
          }
        }

        @keyframes float {
          0%, 100% {
            transform: translate3d(0, 0, 0) rotate(0deg);
          }
          25% {
            transform: translate3d(-5px, -15px, 0) rotate(90deg);
          }
          50% {
            transform: translate3d(0, -20px, 0) rotate(180deg);
          }
          75% {
            transform: translate3d(5px, -15px, 0) rotate(270deg);
          }
        }

        @keyframes spin {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }

        @keyframes pulse {
          0%, 100% {
            opacity: 1;
            transform: scale(1);
          }
          50% {
            opacity: 0.7;
            transform: scale(1.05);
          }
        }

        @keyframes slideInFromRight {
          0% {
            transform: translate3d(100%, 0, 0) scale(0.9) rotate(-10deg);
            opacity: 0;
          }
          60% {
            transform: translate3d(-5%, 0, 0) scale(1.02) rotate(2deg);
            opacity: 0.9;
          }
          100% {
            transform: translate3d(0, 0, 0) scale(1) rotate(0deg);
            opacity: 1;
          }
        }

        @keyframes glow {
          0%, 100% {
            filter: drop-shadow(0 0 5px currentColor);
          }
          50% {
            filter: drop-shadow(0 0 20px currentColor) drop-shadow(0 0 30px currentColor);
          }
        }

        /* Enhanced animations */
        .animate-spin {
          animation: spin 1s linear infinite;
        }

        .animate-pulse {
          animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        .animate-glow {
          animation: glow 2s ease-in-out infinite;
        }

        /* Force hardware acceleration */
        .hw-accelerate {
          transform: translateZ(0);
          backface-visibility: hidden;
          perspective: 1000px;
        }

        /* Smooth scrolling optimization */
        .scroll-container {
          transform: translateZ(0);
          -webkit-overflow-scrolling: touch;
          scroll-behavior: smooth;
        }

        /* Custom gradient animations */
        @keyframes gradient-shift {
          0%, 100% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
        }

        .animate-gradient {
          background-size: 200% 200%;
          animation: gradient-shift 3s ease infinite;
        }
      `}</style>
    </div>
  );
};

export default LiveQuiz;



