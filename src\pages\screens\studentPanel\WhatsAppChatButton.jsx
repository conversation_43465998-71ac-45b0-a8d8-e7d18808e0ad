import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCommentDots } from '@fortawesome/free-solid-svg-icons';

const WhatsAppChatButton = ({
  phoneNumber = '919600780738',
  message = '',
  showDescription = true
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasShownInitial, setHasShownInitial] = useState(false);
  const formattedNumber = `+${phoneNumber.slice(0, 2)} ${phoneNumber.slice(2, 7)} ${phoneNumber.slice(7)}`;
  const whatsappLink = `https://wa.me/${phoneNumber}${message ? `?text=${encodeURIComponent(message)}` : ''}`;

  useEffect(() => {
    // Show tooltip for 2 seconds on initial render
    if (showDescription && !hasShownInitial) {
      setIsVisible(true);
      const timer = setTimeout(() => {
        setIsVisible(false);
        setHasShownInitial(true);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [showDescription, hasShownInitial]);

  return (
    <div className="fixed top-24 right-6 z-50 md:right-8">
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: 'easeOut' }}
        className="relative"
      >
        {/* Floating orb for glassmorphism background effect */}
        <motion.div
          animate={{
            scale: [1, 1.08, 1],
            opacity: [0.2, 0.4, 0.2]
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />

        {/* Main button container with neumorphic shadow */}
        <motion.div
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="relative group"
          onHoverStart={() => showDescription && setIsVisible(true)}
          onHoverEnd={() => showDescription && setIsVisible(false)}
        >
          <a
            href={whatsappLink}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center justify-center w-14 h-14 bg-[#25D366] hover:bg-[#128C7E] text-white rounded-full shadow-[0_4px_14px_rgba(0,0,0,0.15)] hover:shadow-[0_6px_20px_rgba(0,0,0,0.2)] transition-all duration-300 relative overflow-hidden"
          >
            <img
              src="https://upload.wikimedia.org/wikipedia/commons/6/6b/WhatsApp.svg"
              alt="WhatsApp Logo"
              className="w-8 h-8 object-contain"
            />
          </a>

          {/* Tooltip with glassmorphism effect */}
          <AnimatePresence>
            {isVisible && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3, ease: 'easeOut' }}
                className="absolute right-full bottom-0 mr-4 bg-white/80 backdrop-blur-md text-gray-800 p-4 rounded-xl shadow-xl w-64 md:w-72 border border-white/20"
                style={{ transformOrigin: 'right center', pointerEvents: 'none' }}
              >
                <div className="flex items-start gap-3">
                  <FontAwesomeIcon
                    icon={faCommentDots}
                    className="text-[#25D366] mt-1 flex-shrink-0"
                  />
                  <div>
                    <p className="text-sm font-semibold text-gray-900">Need instant help?</p>
                    <p className="text-xs text-gray-600 mb-2">We're available 24/7 on WhatsApp</p>
                    <p className="text-xs font-mono bg-gray-100/50 p-2 rounded-md break-all">
                      {formattedNumber}
                    </p>
                  </div>
                </div>
                <div className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-2 w-3 h-3 bg-white/80 backdrop-blur-md rotate-45 border-r border-b border-white/20"></div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default WhatsAppChatButton;
