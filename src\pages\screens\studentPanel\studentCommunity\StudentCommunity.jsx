import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence, useAnimation } from 'framer-motion';
import {
  MessageCircle,
  Send,
  Users,
  BookOpen,
  Lightbulb,
  Rocket,
  Smile,
  ChevronRight,
  Bookmark,
  FileText,
  HelpCircle,
  Award
} from 'lucide-react';

const StudentCommunity = () => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      user: '<PERSON><PERSON><PERSON>',
      text: 'Hey everyone! Any tips for mastering Organic Chemistry?',
      timestamp: '10:30 AM',
      avatar: 'AS',
      color: 'from-purple-500 to-purple-600'
    },
    {
      id: 2,
      user: '<PERSON><PERSON>',
      text: 'Focus on reaction mechanisms and make flashcards for named reactions!',
      timestamp: '10:32 AM',
      avatar: 'PP',
      color: 'from-pink-500 to-pink-600'
    },
    {
      id: 3,
      user: '<PERSON><PERSON><PERSON>',
      text: 'I found making mind maps super helpful. Want me to share some?',
      timestamp: '10:34 AM',
      avatar: 'RS',
      color: 'from-blue-500 to-blue-600'
    }
  ]);

  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [activeTab, setActiveTab] = useState('chat');
  const messagesEndRef = useRef(null);
  const controls = useAnimation();

  // Scroll to the latest message
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Simulate typing indicator
  useEffect(() => {
    if (newMessage.length > 0) {
      setIsTyping(true);
      const timer = setTimeout(() => setIsTyping(false), 1500);
      return () => clearTimeout(timer);
    } else {
      setIsTyping(false);
    }
  }, [newMessage]);

  // Handle sending a message
  const handleSendMessage = async () => {
    if (newMessage.trim() === '') return;

    const newMsg = {
      id: messages.length + 1,
      user: 'You',
      text: newMessage,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      avatar: 'YO',
      color: 'from-green-500 to-green-600'
    };

    // Animation sequence
    await controls.start({
      scale: [1, 1.1, 1],
      transition: { duration: 0.3 }
    });

    setMessages([...messages, newMsg]);
    setNewMessage('');

    // Simulate reply after 1-2 seconds
    if (Math.random() > 0.3) {
      setTimeout(
        () => {
          const replies = [
            "That's a great point! I agree completely.",
            'Have you checked the NCERT for that topic?',
            'Let me find some resources for you...',
            'We should discuss this in our study group!',
            'I had the same question last week!'
          ];
          const randomReply = replies[Math.floor(Math.random() * replies.length)];

          const botMsg = {
            id: messages.length + 2,
            user: ['Neha Gupta', 'Vikram Joshi', 'Ananya Iyer'][Math.floor(Math.random() * 3)],
            text: randomReply,
            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            avatar: ['NG', 'VJ', 'AI'][Math.floor(Math.random() * 3)],
            color: [
              'from-yellow-500 to-yellow-600',
              'from-red-500 to-red-600',
              'from-indigo-500 to-indigo-600'
            ][Math.floor(Math.random() * 3)]
          };

          setMessages((prev) => [...prev, botMsg]);
        },
        1000 + Math.random() * 1000
      );
    }
  };

  // Handle Enter key press
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        when: 'beforeChildren',
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 10
      }
    }
  };

  const messageVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 15
      }
    },
    exit: { opacity: 0, x: -50 }
  };

  const senderMessageVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 15
      }
    },
    exit: { opacity: 0, x: 50 }
  };

  const typingVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 },
    exit: { opacity: 0 }
  };

  const bubbleVariants = {
    initial: { scale: 0.8, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.8, opacity: 0 }
  };

  const tabVariants = {
    active: {
      backgroundColor: '#3B82F6',
      color: '#FFFFFF',
      scale: 1.05
    },
    inactive: {
      backgroundColor: '#EFF6FF',
      color: '#3B82F6',
      scale: 1
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 flex justify-center items-center p-4 sm:p-8">
      <motion.div
        initial={{ opacity: 0, y: 20, scale: 0.98 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.5, type: 'spring' }}
        className="w-full max-w-4xl bg-white rounded-2xl shadow-xl overflow-hidden flex flex-col"
        style={{ height: '90vh', boxShadow: '0 20px 40px rgba(79, 70, 229, 0.15)' }}
      >
        {/* Header */}
        <motion.header
          className="bg-gradient-to-r from-blue-600 to-indigo-700 p-6 text-white relative overflow-hidden"
          initial={{ y: -50 }}
          animate={{ y: 0 }}
          transition={{ type: 'spring', stiffness: 100 }}
        >
          <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full transform translate-x-16 -translate-y-16"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full transform -translate-x-24 translate-y-16"></div>

          <div className="relative z-10 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ repeat: Infinity, repeatType: 'reverse', duration: 2 }}
                className="bg-white/20 p-2 rounded-lg"
              >
                <Users className="h-8 w-8" />
              </motion.div>
              <div>
                <h1 className="text-2xl font-bold">Study Squad</h1>
                <p className="text-blue-100 flex items-center">
                  {isTyping ? (
                    <motion.span
                      variants={typingVariants}
                      initial="hidden"
                      animate="visible"
                      exit="exit"
                      className="flex items-center"
                    >
                      <span className="flex space-x-1 mr-2">
                        {[...Array(3)].map((_, i) => (
                          <motion.span
                            key={i}
                            animate={{ y: [0, -3, 0] }}
                            transition={{
                              repeat: Infinity,
                              duration: 1.2,
                              delay: i * 0.2
                            }}
                            className="block h-2 w-2 rounded-full bg-blue-200"
                          />
                        ))}
                      </span>
                      Typing...
                    </motion.span>
                  ) : (
                    <span>Active now: {messages.length + 5} students</span>
                  )}
                </p>
              </div>
            </div>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white/20 p-2 rounded-lg"
            >
              <ChevronRight className="h-5 w-5" />
            </motion.button>
          </div>
        </motion.header>

        {/* Chat Feed */}
        <motion.section
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="flex-1 p-4 overflow-y-auto bg-gradient-to-b from-white to-blue-50"
        >
          <motion.div className="space-y-4">
            <motion.div variants={itemVariants} className="flex justify-center">
              <div className="bg-blue-100 text-blue-800 text-sm px-4 py-2 rounded-full inline-flex items-center shadow-sm">
                <BookOpen className="h-4 w-4 mr-2" />
                Today, July 3, 2025
              </div>
            </motion.div>

            <AnimatePresence>
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  variants={message.user === 'You' ? senderMessageVariants : messageVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  className={`flex ${message.user === 'You' ? 'justify-end' : 'justify-start'}`}
                  layout
                >
                  {message.user !== 'You' && (
                    <motion.div
                      className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold bg-gradient-to-br ${message.color} mr-3 self-end shadow-md`}
                      variants={bubbleVariants}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      {message.avatar}
                    </motion.div>
                  )}

                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    className={`max-w-xs sm:max-w-md rounded-2xl p-4 relative ${
                      message.user === 'You'
                        ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-br-none shadow-lg'
                        : 'bg-white border border-gray-200 rounded-bl-none shadow-sm'
                    }`}
                  >
                    {message.user !== 'You' && (
                      <div
                        className="font-bold text-sm mb-1"
                        style={{
                          color: message.color.includes('blue')
                            ? '#3B82F6'
                            : message.color.includes('purple')
                              ? '#8B5CF6'
                              : message.color.includes('pink')
                                ? '#EC4899'
                                : '#10B981'
                        }}
                      >
                        {message.user}
                      </div>
                    )}
                    <p className={message.user === 'You' ? 'text-white' : 'text-gray-800'}>
                      {message.text}
                    </p>
                    <div
                      className={`text-xs mt-1 flex items-center ${
                        message.user === 'You' ? 'text-blue-100' : 'text-gray-500'
                      }`}
                    >
                      {message.timestamp}
                      {message.user === 'You' && (
                        <span className="ml-1 inline-flex">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-3 w-3"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </span>
                      )}
                    </div>

                    {message.user === 'You' ? (
                      <motion.div
                        className="absolute -right-2 bottom-0 w-4 h-4 overflow-hidden"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                      >
                        <div className="absolute w-4 h-4 bg-indigo-600 transform rotate-45 origin-bottom-left" />
                      </motion.div>
                    ) : (
                      <motion.div
                        className="absolute -left-2 bottom-0 w-4 h-4 overflow-hidden"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                      >
                        <div className="absolute w-4 h-4 bg-white border-l border-b border-gray-200 transform rotate-45 origin-bottom-right" />
                      </motion.div>
                    )}
                  </motion.div>
                </motion.div>
              ))}
            </AnimatePresence>
            <div ref={messagesEndRef} />
          </motion.div>
        </motion.section>

        {/* Quick Tips */}
        <motion.div
          className="px-4 py-3 bg-white border-t border-gray-200 overflow-x-auto"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <div className="flex space-x-2">
            {[
              { text: 'NCERT is key!', icon: <FileText size={16} /> },
              { text: 'Practice daily', icon: <Rocket size={16} /> },
              { text: 'Join study group', icon: <Users size={16} /> },
              { text: 'Ask doubts', icon: <HelpCircle size={16} /> }
            ].map((tip, i) => (
              <motion.button
                key={i}
                whileHover={{ y: -2, boxShadow: '0 4px 12px rgba(79, 70, 229, 0.2)' }}
                whileTap={{ scale: 0.95 }}
                className="bg-blue-50 hover:bg-blue-100 text-blue-700 text-sm px-3 py-2 rounded-full whitespace-nowrap flex items-center transition-all"
                onClick={() => setNewMessage(tip.text)}
              >
                <span className="mr-2">{tip.icon}</span>
                {tip.text}
              </motion.button>
            ))}
          </div>
        </motion.div>

        {/* Message Input */}
        <motion.section
          className="p-4 bg-white border-t border-gray-200"
          initial={{ y: 20 }}
          animate={{ y: 0 }}
          transition={{ type: 'spring' }}
        >
          <div className="flex items-center space-x-3">
            <motion.button
              whileHover={{ scale: 1.1, rotate: 10 }}
              whileTap={{ scale: 0.9 }}
              className="p-2 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 transition-colors"
            >
              <Smile className="h-5 w-5" />
            </motion.button>

            <motion.div className="flex-1 relative" animate={controls}>
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask your study question..."
                className="w-full p-3 pr-12 rounded-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm transition-all"
              />
              {newMessage && (
                <motion.button
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0 }}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-blue-100 hover:bg-blue-200 p-1 rounded-full transition-colors"
                  onClick={() => setNewMessage('')}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 text-blue-600"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                </motion.button>
              )}
            </motion.div>

            <motion.button
              onClick={handleSendMessage}
              disabled={!newMessage.trim()}
              whileHover={
                newMessage.trim()
                  ? { scale: 1.1, boxShadow: '0 4px 14px rgba(79, 70, 229, 0.4)' }
                  : {}
              }
              whileTap={newMessage.trim() ? { scale: 0.9 } : {}}
              className={`p-3 rounded-full shadow-md transition-all ${
                newMessage.trim()
                  ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white'
                  : 'bg-gray-200 text-gray-400 cursor-not-allowed'
              }`}
            >
              <Send className="h-5 w-5" />
            </motion.button>
          </div>
        </motion.section>
      </motion.div>
    </div>
  );
};

export default StudentCommunity;
