'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Play,
  Clock,
  BookOpen,
  ChevronRight,
  Atom,
  FlaskConical,
  Leaf,
  BrainCircuit,
  GraduationCap
} from 'lucide-react';

const RecordedVideos = () => {
  const [selectedExam, setSelectedExam] = useState('JEE');
  const [selectedSubject, setSelectedSubject] = useState('Physics');
  const [hoveredVideo, setHoveredVideo] = useState(null);

  const subjectIcons = {
    Physics: <Atom className="text-student" />,
    Chemistry: <FlaskConical className="text-student" />,
    Mathematics: <BrainCircuit className="text-student" />,
    Biology: <Leaf className="text-student" />
  };

  // Dummy video data for NEET and JEE
  const videoData = {
    JEE: {
      Physics: [
        {
          id: 1,
          title: 'Kinematics and Motion',
          duration: '18:40',
          thumbnail:
            'https://t4.ftcdn.net/jpg/04/38/69/77/360_F_438697752_bIJMNtw4TvQGq9T3k7JnhmOei91WHuGj.jpg'
        },
        {
          id: 2,
          title: 'Electromagnetism Basics',
          duration: '22:15',
          thumbnail:
            'https://t4.ftcdn.net/jpg/05/93/28/61/360_F_593286170_6Bbb66Umxv0MgE9whrocGcrvyC27g9vY.jpg'
        },
        {
          id: 3,
          title: 'Thermodynamics',
          duration: '20:30',
          thumbnail:
            'https://cdn1.byjus.com/wp-content/uploads/2018/11/chemistry/2015/12/29114819/Thermodynamics-2.png'
        }
      ],
      Chemistry: [
        {
          id: 1,
          title: 'Organic Chemistry Fundamentals',
          duration: '16:50',
          thumbnail:
            'https://t3.ftcdn.net/jpg/08/03/41/62/360_F_803416239_ppBKwxVvjpfKqpskGXlvRAa0uxBnSB9Y.jpg'
        },
        {
          id: 2,
          title: 'Chemical Bonding',
          duration: '19:25',
          thumbnail:
            'https://img.freepik.com/free-vector/chalkboard-background-with-chemistry-information_23-2148159091.jpg?semt=ais_hybrid&w=740'
        },
        {
          id: 3,
          title: 'Thermodynamics in Chemistry',
          duration: '17:45',
          thumbnail:
            'https://media.istockphoto.com/id/545286316/photo/checking-the-chemical-formula-in-academic-laboratory.jpg?s=612x612&w=0&k=20&c=AQUOASPTFDHb9ellPDcUqGKWvIJhcEkervOxD_z94rM='
        }
      ],
      Mathematics: [
        {
          id: 1,
          title: 'Vectors and 3D Geometry',
          duration: '21:10',
          thumbnail:
            'https://img.freepik.com/free-vector/isometric-maths-material-background_23-2148146102.jpg?semt=ais_hybrid&w=740'
        },
        {
          id: 2,
          title: 'Calculus: Integration',
          duration: '23:30',
          thumbnail:
            'https://img.freepik.com/free-vector/maths-chalkboard_23-2148178220.jpg?semt=ais_items_boosted&w=740'
        },
        {
          id: 3,
          title: 'Probability and Statistics',
          duration: '19:00',
          thumbnail:
            'https://img.freepik.com/free-vector/cartoon-maths-elements-background_52683-9223.jpg?semt=ais_hybrid&w=740'
        }
      ]
    },
    NEET: {
      Physics: [
        {
          id: 1,
          title: 'Mechanics for NEET',
          duration: '17:20',
          thumbnail:
            'https://media.istockphoto.com/id/1866121335/photo/physics-and-mathematics.jpg?s=612x612&w=0&k=20&c=OZmyFAhrYgv-61E3UBjii7R5rLqp5cNdokXSuoTCpiY='
        },
        {
          id: 2,
          title: 'Optics and Waves',
          duration: '20:45',
          thumbnail: 'https://i.pinimg.com/originals/da/cf/ef/dacfef44cb88f842525c90a9115fa8ae.jpg'
        },
        {
          id: 3,
          title: 'Electrostatics',
          duration: '18:55',
          thumbnail:
            'https://t3.ftcdn.net/jpg/02/06/49/56/360_F_206495605_eoaWBcift2BH2GzE5Ur7y4OKpFYhbaq5.jpg'
        }
      ],
      Chemistry: [
        {
          id: 1,
          title: 'Biomolecules',
          duration: '15:30',
          thumbnail:
            'https://image.slidesharecdn.com/biochemistrybiomolecules-200413200303/85/Biochemistry-Biomolecules-and-Cell-An-Introduction-14-320.jpg'
        },
        {
          id: 2,
          title: 'Coordination Compounds',
          duration: '18:10',
          thumbnail:
            'https://img.freepik.com/free-vector/science-chemistry-sketch-chalkboard-icons-set-with-elements-combinations_1284-6077.jpg?semt=ais_hybrid&w=740'
        },
        {
          id: 3,
          title: 'Physical Chemistry Basics',
          duration: '16:40',
          thumbnail:
            'https://t3.ftcdn.net/jpg/06/87/89/86/360_F_687898644_R1Sb6wF9WO0t7xhyD2ioVfHR8n7wah6y.jpg'
        }
      ],
      Biology: [
        {
          id: 1,
          title: 'Human Physiology',
          duration: '22:00',
          thumbnail:
            'https://t3.ftcdn.net/jpg/00/85/94/80/360_F_85948050_pnCz9BxRzGqFbp5e2mR7RmaJG8e2kKFP.jpg'
        },
        {
          id: 2,
          title: 'Genetics and Evolution',
          duration: '19:50',
          thumbnail:
            'https://img.freepik.com/free-photo/scientist-analyzes-bacterium-with-high-scale-magnification-generated-by-ai_188544-27928.jpg?semt=ais_items_boosted&w=740'
        },
        {
          id: 3,
          title: 'Plant Diversity',
          duration: '17:15',
          thumbnail:
            'https://thumbs.dreamstime.com/b/biology-laboratory-nature-science-plants-biochemistry-structure-green-background-162019753.jpg'
        }
      ]
    }
  };

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  return (
    <div className="p-4 sm:p-6 md:p-8 lg:p-10 max-w-7xl mx-auto bg-white min-h-screen">
      {/* Header Section */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ staggerChildren: 0.1 }}
        className="mb-6 sm:mb-8"
      >
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 sm:mb-6">
          <div className="flex items-center gap-2 sm:gap-3">
            <div className="p-2 sm:p-3 bg-student/10 rounded-xl">
              <GraduationCap className="text-student w-5 h-5 sm:w-6 sm:h-6" />
            </div>
            <div>
              <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Recorded Videos</h1>
              <p className="text-sm sm:text-base text-gray-600">
                Access your course materials anytime
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2 px-3 sm:px-4 py-1.5 sm:py-2 bg-student/5 rounded-lg border border-student/10 mt-4 sm:mt-0">
            <BookOpen className="text-student w-4 h-4 sm:w-5 sm:h-5" />
            <span className="text-student font-medium text-sm sm:text-base">
              {selectedExam} Preparation
            </span>
          </div>
        </div>
      </motion.div>

      {/* Filter Section */}
      <motion.div
        className="mb-6 sm:mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <div className="bg-[var(--color-student)] border border-gray-200 rounded-2xl p-4 sm:p-6 shadow-sm">
          <div className="grid grid-cols-1 gap-4 sm:gap-6">
            {/* Exam Selector */}

            {/* Subject Selector */}
            <div>
              <label className="block text-sm font-semibold text-white mb-2 sm:mb-3">
                Select Subject
              </label>
              <div className="flex flex-wrap gap-2">
                {Object.keys(videoData[selectedExam]).map((subject) => {
                  const Icon = {
                    Physics: Atom,
                    Chemistry: FlaskConical,
                    Mathematics: BrainCircuit,
                    Biology: Leaf
                  }[subject];

                  return (
                    <motion.button
                      key={subject}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => setSelectedSubject(subject)}
                      className={`flex items-center gap-2 py-1.5 sm:py-2 px-3 sm:px-4 rounded-lg text-xs sm:text-sm font-medium transition-all border ${
                        selectedSubject === subject
                          ? 'bg-[var(--color-counselor)] text-white border-student/20'
                          : 'bg-gray-50 text-gray-600 border-gray-200 hover:bg-student/5 hover:border-student/20'
                      }`}
                    >
                      <Icon className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                      {subject}
                    </motion.button>
                  );
                })}
              </div>
            </div>
            <div>
              <label className="block text-sm font-semibold text-white mb-2 sm:mb-3">
                Select Courses
              </label>
              <div className="flex gap-2">
                {['JEE', 'NEET'].map((exam) => (
                  <motion.button
                    key={exam}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => {
                      setSelectedExam(exam);
                      setSelectedSubject(exam === 'JEE' ? 'Physics' : 'Biology');
                    }}
                    className={`flex-1 py-2 sm:py-3 px-4 sm:px-6 rounded-xl text-sm font-semibold transition-all border-2 ${
                      selectedExam === exam
                        ? 'bg-[var(--color-counselor)] text-white border-student shadow-lg shadow-student/20'
                        : 'bg-white text-gray-600 border-gray-200 hover:border-student/30 hover:text-student'
                    }`}
                  >
                    {exam}
                  </motion.button>
                ))}
              </div>
            </div>
          </div>

          {/* Current Selection Display */}
          <motion.div
            className="mt-4 pt-4 border-t border-gray-100 flex items-center gap-2 text-xs sm:text-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <span className="text-white">Currently viewing:</span>
            <span className="font-semibold text-student">{selectedExam}</span>
            <ChevronRight className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-gray-400" />
            <div className="flex items-center gap-1">
              {React.cloneElement(subjectIcons[selectedSubject], {
                className: 'w-3.5 h-3.5 sm:w-4 sm:h-4'
              })}
              <span className="font-semibold text-white">{selectedSubject}</span>
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Video Grid */}
      <motion.div
        variants={container}
        initial="hidden"
        animate="show"
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 hover:cursor-pointer"
      >
        {videoData[selectedExam][selectedSubject].map((video) => (
          <motion.div
            key={video.id}
            variants={item}
            whileHover={{ y: -4 }}
            onHoverStart={() => setHoveredVideo(video.id)}
            onHoverEnd={() => setHoveredVideo(null)}
            className="bg-white rounded-2xl border border-gray-200 overflow-hidden group hover:shadow-xl hover:shadow-student/10 transition-all duration-300"
          >
            <div className="relative overflow-hidden">
              <img
                src={video.thumbnail || '/placeholder.svg'}
                alt={video.title}
                className="w-full h-40 sm:h-48 object-cover transform group-hover:scale-105 transition-transform duration-500"
              />

              {/* Play button overlay */}
              <AnimatePresence>
                {hoveredVideo === video.id && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="absolute inset-0 flex items-center justify-center bg-black/40 backdrop-blur-sm"
                  >
                    <motion.div
                      initial={{ scale: 0.8 }}
                      animate={{ scale: 1 }}
                      className="p-3 sm:p-4 bg-white rounded-full shadow-lg"
                    >
                      <Play className="text-student w-6 h-6 sm:w-8 sm:h-8" fill="currentColor" />
                    </motion.div>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Duration badge */}
              <div className="absolute top-2 sm:top-3 right-2 sm:right-3 bg-black/70 text-white px-2 sm:px-3 py-1 rounded-full text-xs font-medium flex items-center backdrop-blur-sm">
                <Clock className="mr-1 w-2.5 h-2.5 sm:w-3 sm:h-3" />
                {video.duration}
              </div>
            </div>

            <div className="p-4 sm:p-6">
              <h3 className="text-base sm:text-lg font-bold text-gray-900 mb-2 sm:mb-3 line-clamp-2 leading-tight">
                {video.title}
              </h3>

              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="w-full bg-student text-white py-2 sm:py-3 rounded-xl flex items-center justify-center gap-2 font-semibold hover:bg-student/90 transition-colors shadow-lg shadow-student/20"
              >
                <Play className="w-4 h-4 sm:w-5 sm:h-5" />
                Watch Now
              </motion.button>
            </div>

            {/* Hover border effect */}
            <motion.div
              className="absolute inset-0 border-2 border-transparent rounded-2xl pointer-events-none"
              animate={{
                borderColor: hoveredVideo === video.id ? 'rgba(37, 99, 235, 0.2)' : 'transparent'
              }}
            />
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
};

export default RecordedVideos;
