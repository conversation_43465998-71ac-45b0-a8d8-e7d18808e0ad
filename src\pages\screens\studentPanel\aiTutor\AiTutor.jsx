import React, { useEffect, useState, useMemo } from 'react';
import { motion, useReducedMotion } from 'framer-motion';

import {
  School,
  AlertTriangle,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  Sparkles,
  Compass,
  BookOpen,
  Landmark,
  Layers,
  BrainCircuit,
  FileText,
  Globe,
  FlaskConical,
  BookText,
  ArrowRight,
  Telescope,
  Atom,
  Music,
  Palette,
  Dna,
  Orbit,
  Target,
  Video,
  NotebookPen,
  ScanSearch,
  BookOpenCheck,
  MessageCircleQuestionIcon,
  BookOpenText
} from 'lucide-react';
import Pdf from './Pdf';
import LanguageSelector from './LanguageSelector';
import ContentQuiz from './ContentQuiz';
const AiTutor = () => {
  const [subjects, setSubjects] = useState([]);
  const [topics, setTopics] = useState([]);
  const [subTopics, setSubTopics] = useState([]);
  const [selectorUrl, setSelectorUrl] = useState('');
  const [processId, setProcessId] = useState(null);
  const [subject, setSubject] = useState('');
  const [selectedSubjectId, setSelectedSubjectId] = useState(null);
  const [selectedTopicId, setSelectedTopicId] = useState(null);
  const [selectedSubTopicId, setSelectedSubTopicId] = useState(null);
  const [selectedSubjectName, setSelectedSubjectName] = useState('');
  const [selectedTopicName, setSelectedTopicName] = useState('');
  const [selectedSubTopicName, setSelectedSubTopicName] = useState('');
  const [showPdf, setShowPdf] = useState(false);
  const [finalPdfUrl, setFinalPdfUrl] = useState('');
  const [showQuiz, setShowQuiz] = useState(false);
  const [currentView, setCurrentView] = useState('subjects');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const shouldReduceMotion = useReducedMotion();

  const iconMap = useMemo(
    () => ({
      Math: <BookOpen size={28} />,
      Science: <Atom size={28} />,
      History: <Landmark size={28} />,
      English: <BookText size={28} />,
      Physics: <Orbit size={28} />,
      Chemistry: <FlaskConical size={28} />,
      Biology: <Dna size={28} />,
      Art: <Palette size={28} />,
      Music: <Music size={28} />,
      default: <BookOpen size={28} />
    }),
    []
  );

  const hoverTransforms = useMemo(
    () => [
      { rotateY: 15, rotateX: -5, scale: 1.03, z: 30 },
      { rotateY: -10, rotateX: 8, scale: 1.03, z: 20 },
      { rotateY: 5, rotateX: -12, scale: 1.03, z: 25 },
      { rotateY: -15, rotateX: 5, scale: 1.03, z: 35 },
      { rotateY: 10, rotateX: 15, scale: 1.03, z: 30 },
      { rotateY: -5, rotateX: -8, scale: 1.03, z: 20 }
    ],
    []
  );

  useEffect(() => {
    setIsLoading(true);
    fetch('https://sasthra.in/api/subjects')
      .then((res) => res.json())
      .then((data) => {
        setSubjects(data);
        setIsLoading(false);
      })
      .catch((err) => {
        console.error('Error fetching subjects:', err);
        setError('Failed to load subjects. Please try again.');
        setIsLoading(false);
      });
  }, []);

  const handleSubjectClick = (subject) => {
    setSelectedSubjectId(subject.subject_id);
    setSelectedSubjectName(subject.subject_name);
    setSelectedTopicId(null);
    setSelectedSubTopicId(null);
    setSubTopics([]);
    setSelectorUrl('');
    setShowPdf(false);
    setShowQuiz(false);
    setFinalPdfUrl('');
    setCurrentView('topics');
    setIsLoading(true);

    fetch(`https://sasthra.in/api/topics/${subject.subject_id}`)
      .then((res) => res.json())
      .then((data) => {
        setTopics(data);
        setIsLoading(false);
      })
      .catch((err) => {
        console.error('Error fetching topics:', err);
        setError('Failed to load topics. Please try again.');
        setIsLoading(false);
      });
  };

  const handleTopicClick = (topic) => {
    setSelectedTopicId(topic.topic_id);
    setSelectedTopicName(topic.topic_name);
    setSelectedSubTopicId(null);
    setSelectorUrl('');
    setShowPdf(false);
    setShowQuiz(false);
    setFinalPdfUrl('');
    setCurrentView('subtopics');
    setIsLoading(true);

    fetch(`https://sasthra.in/api/subtopics/${topic.topic_id}`)
      .then((res) => res.json())
      .then((data) => {
        setSubTopics(data);
        setIsLoading(false);
      })
      .catch((err) => {
        console.error('Error fetching subtopics:', err);
        setError('Failed to load subtopics. Please try again.');
        setIsLoading(false);
      });
  };

  const handleSubTopicClick = (subTopic) => {
    setSelectedSubTopicId(subTopic.sub_topic_id);
    setSelectedSubTopicName(subTopic.sub_topic_name);
    setShowPdf(false);
    setShowQuiz(false);
    setFinalPdfUrl('');
    setCurrentView('selector');
    setIsLoading(true);

    fetch(`https://sasthra.in/api/selector-url/${subTopic.sub_topic_id}`)
      .then((res) => res.json())
      .then((data) => {
        setSubject(data.subject_name);
        setProcessId(data.process_selector_id);
        setSelectorUrl(data.process_selector_url || 'No URL available for this subtopic.');
        setIsLoading(false);
      })
      .catch((err) => {
        console.error('Error fetching selector URL and ID:', err);
        setSelectorUrl('Error fetching URL and ID.');
        setError('Failed to load content. Please try again.');
        setIsLoading(false);
      });
  };

  const handleLanguageSelectorComplete = (url) => {
    setFinalPdfUrl(url);
    setShowPdf(true);
  };

  const handlePdfComplete = () => {
    setShowPdf(false);
    setShowQuiz(true);
  };

  const handleQuizClose = () => {
    setShowQuiz(false);
    setShowPdf(true);
  };

  const handleQuizButtonClick = () => {
    setShowPdf(false);
    setShowQuiz(true);
  };

  const handleBack = () => {
    if (currentView === 'selector') {
      setCurrentView('subtopics');
      setSelectorUrl('');
      setShowPdf(false);
      setShowQuiz(false);
      setFinalPdfUrl('');
    } else if (currentView === 'subtopics') {
      setCurrentView('topics');
      setSubTopics([]);
      setSelectedSubTopicId(null);
    } else if (currentView === 'topics') {
      setCurrentView('subjects');
      setTopics([]);
      setSelectedTopicId(null);
    }
  };

  const getBreadcrumb = () => {
    const breadcrumb = [];
    if (selectedSubjectName) breadcrumb.push(selectedSubjectName);
    if (selectedTopicName) breadcrumb.push(selectedTopicName);
    if (selectedSubTopicName) breadcrumb.push(selectedSubTopicName);
    return breadcrumb.join(' > ');
  };

  const SubjectCard = React.memo(({ subject, index }) => {
    const subjectName = subject.subject_name.split(' ')[0];
    const subjectIcon = iconMap[subjectName] || iconMap.default;

    // Card deal animation variants
    const cardVariants = {
      hidden: {
        opacity: 0,
        y: 50,
        rotate: index % 2 ? 10 : -10,
        scale: 0.8
      },
      visible: {
        opacity: 1,
        y: 0,
        rotate: 0,
        scale: 1,
        transition: {
          delay: index * 0.15,
          type: 'spring',
          stiffness: 300,
          damping: 15
        }
      },
      hover: {
        y: -10,
        rotate: index % 2 ? 5 : -5,
        scale: 1.05,
        boxShadow: '0 15px 30px rgba(0,0,0,0.15)',
        transition: { type: 'spring', stiffness: 400 }
      }
    };

    // Magic sparkle particles
    const particles = [...Array(8)].map((_, i) => (
      <motion.div
        key={i}
        className="absolute rounded-full bg-white pointer-events-none"
        style={{
          width: `${Math.random() * 4 + 2}px`,
          height: `${Math.random() * 4 + 2}px`,
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
          opacity: 0
        }}
        animate={{
          opacity: [0, 0.8, 0],
          x: [0, (Math.random() - 0.5) * 20],
          y: [0, (Math.random() - 0.5) * 20]
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          delay: Math.random() * 1.5
        }}
      />
    ));

    return (
      <motion.div
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        whileHover="hover"
        onClick={() => handleSubjectClick(subject)}
        className="relative w-64 h-80 rounded-xl cursor-pointer overflow-hidden"
        style={{
          perspective: '1000px',
          transformStyle: 'preserve-3d',
          background: 'linear-gradient(135deg, #ffffff, #f8f9fa)'
        }}
      >
        {/* Magic card border */}
        <div className="absolute inset-0 rounded-xl border-2 border-white/30 pointer-events-none" />

        {/* Card content */}
        <div className="relative z-10 h-full p-6 flex flex-col">
          {/* Subject icon with magic glow */}
          <motion.div
            className="w-14 h-14 rounded-lg flex items-center justify-center mb-4 self-start"
            style={{
              background: 'rgba(255,255,255,0.7)',
              boxShadow: '0 4px 15px rgba(0,0,0,0.1)'
            }}
            whileHover={{
              rotate: 360,
              transition: { duration: 0.8 }
            }}
          >
            {React.cloneElement(subjectIcon, {
              className: 'text-blue-600',
              size: 28
            })}
          </motion.div>

          {/* Subject name with floating effect */}
          <motion.h3 className="text-2xl font-bold mb-3 text-gray-800" whileHover={{ y: -3 }}>
            {subjectName}
          </motion.h3>

          {/* Description with typing animation */}
          <motion.p
            className="text-sm text-gray-600 mb-6"
            initial={{ opacity: 0 }}
            animate={{
              opacity: 1,
              transition: { delay: index * 0.15 + 0.3 }
            }}
          >
            Explore carefully curated topics
          </motion.p>

          {/* Progress bar with magic pulse */}
          <div className="mt-auto">
            <div className="flex justify-between items-center mb-2">
              <span className="text-xs text-gray-500">Learning Topics</span>
              <motion.span
                className="text-xs font-bold text-blue-600"
                animate={{
                  scale: [1, 1.1, 1],
                  transition: { repeat: Infinity, duration: 2 }
                }}
              >
                100%
              </motion.span>
            </div>
            <div className="relative h-2 bg-[var(--color-student)] rounded-full overflow-hidden">
              <motion.div
                className="absolute top-0 left-0 h-full bg-blue-600 rounded-full"
                initial={{ width: 0 }}
                animate={{
                  width: `${Math.min(subject.topics_count * 10, 100)}%`,
                  transition: {
                    delay: index * 0.15 + 0.5,
                    duration: 0.8
                  }
                }}
              >
                <motion.div
                  className="absolute right-0 top-0 h-full w-1 bg-white/80"
                  animate={{
                    opacity: [0, 1, 0],
                    transition: { repeat: Infinity, duration: 1.5 }
                  }}
                />
              </motion.div>
            </div>
          </div>

          {/* Magic action button */}
          <motion.div
            className="mt-6 self-end"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <button className="px-4 py-2 rounded-lg text-sm font-medium hover:cursor-pointer bg-blue-600/10 text-blue-600 hover:bg-blue-600 hover:text-white transition-all flex items-center">
              Start learning
              <motion.div
                animate={{ x: [0, 4, 0] }}
                transition={{ repeat: Infinity, duration: 1.5 }}
              >
                <ArrowRight className="ml-2" size={16} />
              </motion.div>
            </button>
          </motion.div>
        </div>

        {/* Magic particles */}
        {particles}

        {/* Subtle glow effect */}
        <div className="absolute inset-0 rounded-xl pointer-events-none overflow-hidden">
          <motion.div
            className="absolute -inset-10 bg-blue-600/10 rounded-full filter blur-xl"
            animate={{
              x: [0, 20, 0],
              y: [0, 15, 0],
              opacity: [0.1, 0.3, 0.1]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
          />
        </div>

        {/* Floating shadow */}
        <motion.div
          className="absolute -bottom-3 left-4 right-4 h-4 bg-black/5 blur-md rounded-b-xl"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{
            opacity: 0.1,
            scale: 1,
            transition: { delay: index * 0.15 + 0.3 }
          }}
        />
      </motion.div>
    );
  });

  const TopicCard = React.memo(({ topic, index }) => (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ delay: index * 0.1, type: 'spring' }}
      className="relative isolate group"
    >
      {/* Floating platform base */}
      <motion.div
        className="absolute inset-0 rounded-[20px] bg-gradient-to-br from-blue-500/5 to-purple-500/5 backdrop-blur-sm"
        style={{
          clipPath: 'polygon(0% 0%, 100% 0%, 100% 80%, 80% 100%, 0% 100%)'
        }}
        whileHover={
          shouldReduceMotion
            ? {}
            : {
                y: -10,
                boxShadow: '0 20px 40px rgba(59, 130, 246, 0.15)'
              }
        }
      />

      {/* Holographic content projection */}
      <div className="relative z-10 p-6 cursor-pointer">
        {/* Floating orb icon */}
        <motion.div
          className="w-16 h-16 rounded-full bg-white flex items-center justify-center shadow-lg mb-4 mx-auto"
          whileHover={shouldReduceMotion ? {} : { scale: 1.1 }}
          style={{
            boxShadow: '0 0 20px rgba(99, 102, 241, 0.5)',
            filter: 'drop-shadow(0 0 10px rgba(99, 102, 241, 0.7))'
          }}
        >
          <BookOpenCheck size={24} className="text-blue-500" />
        </motion.div>

        {/* Floating text elements */}
        <motion.div
          className="text-center"
          initial={{ y: 10 }}
          animate={{ y: 0 }}
          transition={{ delay: index * 0.1 + 0.2 }}
        >
          <h3 className="text-2xl font-bold bg-clip-text text-transparent bg-white mb-2">
            {topic.topic_name}
          </h3>
          <p className="text-sm text-gray-300 mb-4 flex items-center justify-center">
            {topic.sub_topics_count} interactive modules
          </p>
        </motion.div>

        {/* Particle connection lines */}
        <div className="relative h-1 my-6 overflow-hidden">
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent"
            initial={{ x: '-100%' }}
            animate={{ x: '100%' }}
            transition={{
              delay: index * 0.1 + 0.4,
              duration: 2,
              repeat: Infinity,
              ease: 'linear'
            }}
          />
        </div>

        {/* Floating action button */}
        <motion.div
          className="w-12 h-12 rounded-full  bg-white/90 backdrop-blur-sm flex items-center justify-center mx-auto shadow-lg"
          whileHover={
            shouldReduceMotion
              ? {}
              : {
                  scale: 1.1,
                  boxShadow: '0 0 20px rgba(255,255,255,0.5)'
                }
          }
          whileTap={shouldReduceMotion ? {} : { scale: 0.95 }}
          onClick={() => handleTopicClick(topic)}
          onKeyDown={(e) => e.key === 'Enter' && handleTopicClick(topic)}
          role="button"
          tabIndex={0}
          aria-label={`Explore ${topic.topic_name}`}
        >
          <ChevronRight size={20} className="text-blue-600" />
        </motion.div>
      </div>

      {/* Holographic glow effects */}
      <motion.div
        className="absolute inset-0 rounded-[20px] border-2 border-blue-400/20 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        style={{
          boxShadow: 'inset 0 0 30px rgba(59, 130, 246, 0.3)'
        }}
      />
      <motion.div className="absolute -inset-2 rounded-[24px] bg-blue-500/10 pointer-events-none opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-500 -z-10" />

      {/* Floating particles */}
      {[1, 2, 3, 4].map((i) => (
        <motion.div
          key={i}
          className="absolute rounded-full bg-blue-400/30 pointer-events-none"
          style={{
            width: `${Math.random() * 6 + 2}px`,
            height: `${Math.random() * 6 + 2}px`,
            left: `${Math.random() * 80 + 10}%`,
            top: `${Math.random() * 80 + 10}%`
          }}
          animate={{
            y: [0, (Math.random() - 0.5) * 20],
            x: [0, (Math.random() - 0.5) * 10],
            opacity: [0.3, 0.7, 0.3]
          }}
          transition={{
            duration: 3 + Math.random() * 4,
            repeat: Infinity,
            ease: 'easeInOut'
          }}
        />
      ))}
    </motion.div>
  ));

  const SubTopicCard = React.memo(({ subTopic, index }) => {
    const [isHovered, setIsHovered] = useState(false);

    return (
      <motion.div
        role="button"
        tabIndex={0}
        aria-label={`Select ${subTopic.sub_topic_name}`}
        onClick={() => handleSubTopicClick(subTopic)}
        onKeyDown={(e) => e.key === 'Enter' && handleSubTopicClick(subTopic)}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.1, type: 'spring' }}
        whileHover={shouldReduceMotion ? {} : { y: -5 }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        className="relative cursor-pointer h-full"
      >
        {/* White card base with subtle shadow */}
        <motion.div
          className="h-full rounded-xl overflow-hidden bg-white border border-gray-100"
          style={{
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
          }}
          animate={
            isHovered && !shouldReduceMotion
              ? {
                  y: -5,
                  boxShadow: '0 8px 30px rgba(0, 0, 0, 0.1)'
                }
              : {}
          }
        >
          {/* Status indicator */}
          <motion.div
            className="absolute top-3 right-3 w-3 h-3 rounded-full bg-green-400"
            animate={
              isHovered && !shouldReduceMotion
                ? {
                    scale: [1, 1.3, 1]
                  }
                : {}
            }
            transition={isHovered ? { duration: 1, repeat: Infinity } : {}}
          />

          {/* Content container */}
          <div className="p-5 h-full flex flex-col">
            {/* Header with icon */}
            <div className="flex items-start space-x-4 mb-5">
              <motion.div
                className="relative z-10"
                animate={
                  !shouldReduceMotion
                    ? {
                        rotate: [0, 5, -5, 0]
                      }
                    : {}
                }
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  repeatDelay: 3
                }}
              >
                <div className="w-12 h-12 rounded-lg flex items-center justify-center bg-blue-50">
                  <BookOpenText size={20} className="text-blue-500" />
                </div>
              </motion.div>

              <h3 className="font-semibold mt-1 text-gray-800">{subTopic.sub_topic_name}</h3>
            </div>

            {/* Progress indicator */}
            <div className="mb-5">
              <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-blue-400 to-blue-600 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${Math.min(100, Math.random() * 70 + 30)}%` }}
                  transition={{ delay: index * 0.1 + 0.3, duration: 1 }}
                />
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {Math.floor(Math.random() * 70 + 30)}% mastered
              </div>
            </div>

            {/* Resource buttons */}
            <div className="mt-auto grid grid-cols-2 gap-3">
              <motion.div
                whileHover={
                  shouldReduceMotion
                    ? {}
                    : {
                        scale: 1.03,
                        backgroundColor: 'rgba(59, 130, 246, 0.05)'
                      }
                }
                whileTap={shouldReduceMotion ? {} : { scale: 0.98 }}
                className="p-2 rounded-lg flex flex-col items-center justify-center border border-gray-200 hover:border-blue-200 transition-all"
              >
                <div className="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mb-1">
                  <MessageCircleQuestionIcon size={16} className="text-blue-500" />
                </div>
                <span className="text-xs text-gray-600">Quiz</span>
                <span className="text-xxs text-gray-400 mt-0.5"> available</span>
              </motion.div>

              <motion.div
                whileHover={
                  shouldReduceMotion
                    ? {}
                    : {
                        scale: 1.03,
                        backgroundColor: 'rgba(234, 179, 8, 0.05)'
                      }
                }
                whileTap={shouldReduceMotion ? {} : { scale: 0.98 }}
                className="p-2 rounded-lg flex flex-col items-center justify-center border border-gray-200 hover:border-yellow-200 transition-all"
              >
                <div className="w-8 h-8 rounded-full bg-yellow-50 flex items-center justify-center mb-1">
                  <NotebookPen size={16} className="text-yellow-500" />
                </div>
                <span className="text-xs text-gray-600">Notes</span>
                <span className="text-xxs text-gray-400 mt-0.5"> available</span>
              </motion.div>
            </div>
          </div>

          {/* Hover border effect */}
          {isHovered && !shouldReduceMotion && (
            <motion.div
              className="absolute inset-0 rounded-xl border-2 border-blue-300 pointer-events-none"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            />
          )}
        </motion.div>

        {/* Subtle floating indicator */}
        {!shouldReduceMotion && (
          <motion.div
            className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-8 h-1 rounded-full bg-blue-400/30"
            animate={{
              scaleX: [1, 1.2, 1],
              opacity: [0.6, 1, 0.6]
            }}
            transition={{
              duration: 2,
              repeat: Infinity
            }}
          />
        )}
      </motion.div>
    );
  });

  if (isLoading) {
    return (
      <motion.div
        className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-900 to-indigo-900 p-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        {/* Floating Book Assembly */}
        <motion.div
          className="relative w-48 h-64 mb-12 perspective-1000"
          animate={{
            y: [0, -15, 0],
            transition: {
              duration: 3,
              repeat: Infinity,
              ease: 'easeInOut'
            }
          }}
        >
          {/* Static Back Cover */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-800 to-blue-700 rounded-lg shadow-2xl border-2 border-blue-600/30" />

          {/* Dynamic Pages Stack */}
          {[0, 1, 2, 3, 4].map((i) => (
            <motion.div
              key={i}
              className="absolute inset-0 bg-gradient-to-br from-blue-50 to-white rounded-r-lg shadow-sm border-r border-t border-b border-blue-100"
              style={{
                transformOrigin: 'left center',
                zIndex: 5 - i
              }}
              animate={{
                rotateY: i === 0 ? [0, -170, 0] : [0, -160 * (1 - i / 10), 0],
                transition: {
                  duration: 3.5,
                  delay: i * 0.15,
                  repeat: Infinity,
                  ease: 'easeInOut'
                }
              }}
            />
          ))}

          {/* Front Cover with Shine */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg shadow-xl z-10 overflow-hidden"
            style={{
              transformOrigin: 'left center',
              boxShadow: '0 10px 25px -5px rgba(59, 130, 246, 0.5)'
            }}
            animate={{
              rotateY: [0, -25, 0],
              transition: {
                duration: 3.5,
                repeat: Infinity,
                ease: 'easeInOut'
              }
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent w-1/2" />
            <div className="absolute right-4 top-1/3 w-10 h-10 bg-yellow-300 rounded-full shadow-md" />
            <div className="absolute right-6 bottom-6 w-20 h-2 bg-white/90 rounded-full" />
            <div className="absolute left-6 top-8 w-12 h-1 bg-white/60 rounded-full" />
          </motion.div>

          {/* Glow Effect */}
          <motion.div
            className="absolute -inset-4 rounded-xl bg-blue-400/10 blur-xl pointer-events-none"
            animate={{
              opacity: [0.3, 0.6, 0.3],
              transition: {
                duration: 3,
                repeat: Infinity
              }
            }}
          />
        </motion.div>

        {/* Loading Content */}
        <div className="text-center max-w-md space-y-6">
          <motion.div
            animate={{
              opacity: [0.9, 1, 0.9],
              transition: {
                duration: 2.5,
                repeat: Infinity
              }
            }}
          >
            <h2 className="text-3xl font-bold text-white mb-2 drop-shadow-md">
              Curating Knowledge
            </h2>
            <p className="text-blue-200 text-lg">
              Loading <span className="font-semibold text-white">{subjects.length}</span> premium
              subjects...
            </p>
          </motion.div>

          {/* DNA-inspired Progress Indicator */}
          <motion.div className="relative h-2 w-full bg-blue-800/50 rounded-full overflow-hidden">
            <motion.div
              className="absolute h-full bg-gradient-to-r from-blue-300 to-blue-500 rounded-full"
              initial={{ width: 0 }}
              animate={{
                width: ['0%', '40%', '80%', '100%'],
                transition: {
                  duration: 3.5,
                  repeat: Infinity,
                  ease: 'anticipate'
                }
              }}
            />
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
              animate={{
                x: ['-100%', '100%'],
                transition: {
                  duration: 2,
                  repeat: Infinity,
                  ease: 'linear'
                }
              }}
            />
          </motion.div>
        </div>

        {/* Floating Blue Particles */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute rounded-full bg-white/20"
              style={{
                width: `${Math.random() * 10 + 4}px`,
                height: `${Math.random() * 10 + 4}px`,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`
              }}
              animate={{
                y: [0, (Math.random() - 0.5) * 100],
                x: [0, (Math.random() - 0.5) * 80],
                opacity: [0, 0.8, 0],
                scale: [0.5, 1.2, 0.5],
                rotate: [0, 180]
              }}
              transition={{
                duration: Math.random() * 10 + 5,
                repeat: Infinity,
                delay: Math.random() * 3
              }}
            />
          ))}
        </div>

        {/* Subtle Branding */}
        <motion.div
          className="absolute bottom-6 text-blue-300/80 text-sm"
          animate={{
            opacity: [0.6, 0.9, 0.6],
            transition: {
              duration: 4,
              repeat: Infinity
            }
          }}
        >
          The smarter way to learn
        </motion.div>
      </motion.div>
    );
  }

  if (error) {
    return (
      <div className="relative h-screen w-full overflow-hidden bg-white flex items-center justify-center p-6">
        {/* Animated grid background */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-grid-white/[0.05] [mask-image:linear-gradient(to_bottom,transparent,black,transparent)]" />
        </div>

        {/* Floating error orb */}
        <motion.div
          className="relative z-10 max-w-md text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Holographic error display */}
          <motion.div
            className="p-8 rounded-2xl backdrop-blur-sm border border-blue-500 mb-6"
            style={{
              background:
                'radial-gradient(circle at center, rgba(239, 68, 68, 0.1) 0%, transparent 70%)',
              boxShadow: '0 0 40px rgba(239, 68, 68, 0.2)'
            }}
            animate={{
              boxShadow: [
                '0 0 30px rgba(239, 68, 68, 0.2)',
                '0 0 50px rgba(239, 68, 68, 0.3)',
                '0 0 30px rgba(239, 68, 68, 0.2)'
              ]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
          >
            {/* Error icon */}
            <motion.div
              className="w-20 h-20 rounded-full mx-auto mb-6 flex items-center justify-center"
              transition={{
                rotate: {
                  duration: 20,
                  repeat: Infinity,
                  ease: 'linear'
                },
                scale: {
                  duration: 3,
                  repeat: Infinity,
                  ease: 'easeInOut'
                }
              }}
            >
              <AlertTriangle size={36} className="text-blue-500" />
            </motion.div>

            {/* Error text */}
            <motion.h2
              className="text-2xl font-bold mb-3 bg-clip-text text-transparent bg-blue-500"
              transition={{
                duration: 3,
                repeat: Infinity
              }}
            >
              System Anomaly Detected
            </motion.h2>

            <motion.p
              className="text-black mb-6 font-mono text-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              {error}
            </motion.p>

            {/* Connection status */}
            <div className="flex items-center justify-center space-x-2 mb-6">
              <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse"></div>
              <span className="text-red-300 text-sm">Connection interrupted</span>
            </div>
          </motion.div>

          {/* Retry button */}
          <motion.button
            className="px-6 py-3 hover:cursor-pointer rounded-full bg-blue-500 font-medium relative overflow-hidden group"
            style={{
              boxShadow: '0 0 20px rgba(99, 102, 241, 0.2)'
            }}
            whileHover={{
              scale: 1.05,
              boxShadow: '0 0 30px rgba(99, 102, 241, 0.4)'
            }}
            whileTap={{ scale: 0.95 }}
            onClick={() => window.location.reload()}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <span className="relative z-10 flex items-center justify-center space-x-2">
              <RefreshCw size={16} className="text-white-400" />
              <span className="bg-clip-text text-transparent bg-white">Attempt System Reboot</span>
            </span>
            <span className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          </motion.button>
        </motion.div>

        {/* Floating particles */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-red-400/20 pointer-events-none"
            style={{
              width: `${Math.random() * 8 + 4}px`,
              height: `${Math.random() * 8 + 4}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`
            }}
            animate={{
              y: [0, (Math.random() - 0.5) * 100],
              x: [0, (Math.random() - 0.5) * 50],
              opacity: [0.2, 0.6, 0.2]
            }}
            transition={{
              duration: 5 + Math.random() * 10,
              repeat: Infinity,
              ease: 'easeInOut'
            }}
          />
        ))}
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-blue-950 dark:bg-slate-900 transition-colors duration-300">
      <motion.header
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ type: 'spring', damping: 10 }}
        className="fixed top-0 left-0 right-0 z-50 backdrop-blur-md bg-white/80 dark:bg-slate-800/80 border-b border-gray-100/50 dark:border-slate-700/50"
      >
        <div className="container mx-auto px-6 py-3">
          <div className="flex items-center justify-between">
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="flex items-center space-x-2 cursor-pointer"
            >
              <div className="w-9 h-9 rounded-full flex items-center justify-center bg-blue-600 dark:bg-blue-400 shadow-lg shadow-blue-600/40 dark:shadow-blue-400/40">
                <School size={16} className="text-white" />
              </div>
              <span className="font-bold text-lg text-slate-800 dark:text-slate-200">
                <span className="text-blue-600 dark:text-blue-400">Sasthra</span>Learning
                <span className="text-blue-600 font-bold dark:text-blue-400">Hub</span>
              </span>
            </motion.div>
            {currentView !== 'subjects' && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleBack}
                className="flex items-center space-x-1 text-sm hover:cursor-pointer font-medium px-3 py-1.5 rounded-full bg-blue-600/15 text-blue-600 dark:bg-blue-400/15 dark:text-blue-400"
              >
                <ChevronLeft size={16} />
                <span>Back</span>
              </motion.button>
            )}
          </div>
        </div>
      </motion.header>

      <main className="container mx-auto px-6 pt-24 pb-12">
        <motion.div className="mb-4 text-sm text-gray-500 dark:text-gray-400">
          <span className="font-medium text-white dark:text-blue-400">Home</span>
          {getBreadcrumb() && (
            <>
              <span className="mx-2"></span>
              <span>{getBreadcrumb()}</span>
            </>
          )}
        </motion.div>

        <motion.div
          key={currentView}
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between">
            <div>
              <motion.div layout className="flex items-center space-x-3">
                <motion.div
                  layoutId="titleIcon"
                  className="w-10 h-10 rounded-lg flex items-center justify-center bg-blue-600/20 text-blue-600 dark:bg-blue-400/20 dark:text-blue-400"
                >
                  {currentView === 'subjects' && <Compass size={20} />}
                  {currentView === 'topics' && <BookOpen size={20} />}
                  {currentView === 'subtopics' && <Layers size={20} />}
                  {currentView === 'selector' && showQuiz && <BrainCircuit size={20} />}
                  {currentView === 'selector' && showPdf && <FileText size={20} />}
                  {currentView === 'selector' && !showPdf && !showQuiz && <Globe size={20} />}
                </motion.div>
                <div>
                  <motion.h1
                    layoutId="titleText"
                    className="text-2xl font-bold text-white dark:text-slate-200"
                  >
                    {currentView === 'subjects' && 'Explore Subjects'}
                    {currentView === 'topics' && selectedSubjectName}
                    {currentView === 'subtopics' && selectedTopicName}
                    {currentView === 'selector' &&
                      (showQuiz ? 'Knowledge Check' : showPdf ? 'Study Materials' : 'Setup')}
                  </motion.h1>
                  <motion.p
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-sm text-white dark:text-slate-300"
                  >
                    {currentView === 'subjects' && 'Choose a subject to begin learning'}
                    {currentView === 'topics' && `${topics.length} topics available`}
                    {currentView === 'subtopics' && `${subTopics.length} learning modules`}
                    {currentView === 'selector' &&
                      (showQuiz
                        ? 'Test your understanding'
                        : showPdf
                          ? 'Study at your own pace'
                          : 'Select your preferences')}
                  </motion.p>
                </div>
              </motion.div>
            </div>
            {currentView === 'selector' && showPdf && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleQuizButtonClick}
                className="flex items-center justify-center space-x-2 px-6 py-3 rounded-full text-base font-medium shadow-md bg-yellow-400 hover:bg-yellow-500 text-slate-900 transition-colors duration-200 w-48"
              >
                <Sparkles size={18} className="flex-shrink-0" />
                <span className="whitespace-nowrap">Practice Quiz</span>
              </motion.button>
            )}
          </div>
        </motion.div>

        {currentView === 'subjects' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="relative py-12"
          >
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute rounded-lg opacity-5"
                  style={{
                    backgroundColor: '#2563eb',
                    width: Math.random() * 150 + 50,
                    height: Math.random() * 150 + 50,
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                    filter: 'blur(10px)'
                  }}
                  animate={
                    shouldReduceMotion
                      ? {}
                      : {
                          x: [0, Math.random() * 100 - 50],
                          y: [0, Math.random() * 100 - 50],
                          rotateZ: [0, Math.random() * 180]
                        }
                  }
                  transition={{
                    duration: Math.random() * 20 + 10,
                    repeat: Infinity,
                    repeatType: 'reverse',
                    ease: 'easeInOut'
                  }}
                />
              ))}
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 justify-center">
              {subjects.map((subject, index) => (
                <SubjectCard key={subject.subject_id} subject={subject} index={index} />
              ))}
            </div>
          </motion.div>
        )}

        {currentView === 'topics' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="grid grid-cols-1 md:grid-cols-2 gap-5 p-2"
          >
            {topics.map((topic, index) => (
              <TopicCard key={topic.topic_id} topic={topic} index={index} />
            ))}
          </motion.div>
        )}

        {currentView === 'subtopics' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5"
          >
            {subTopics.map((subTopic, index) => (
              <SubTopicCard key={subTopic.sub_topic_id} subTopic={subTopic} index={index} />
            ))}
          </motion.div>
        )}

        {currentView === 'selector' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="max-w-7xl mx-auto" // Added responsive padding
          >
            {!showPdf && !showQuiz ? (
              <motion.div
                initial={{ scale: 0.95 }}
                animate={{ scale: 1 }}
                className="flex justify-center w-full"
              >
                <LanguageSelector
                  processId={processId}
                  subject={subject}
                  onComplete={handleLanguageSelectorComplete}
                />
              </motion.div>
            ) : showQuiz ? (
              <div className="w-full bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6">
                <ContentQuiz processId={processId} onClose={handleQuizClose} />
              </div>
            ) : selectorUrl && selectorUrl.startsWith('http') ? (
              <div className="w-full bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden">
                <Pdf
                  fileUrl={selectorUrl}
                  fileName={
                    selectedSubTopicName
                      ? `${selectedSubTopicName.replace(/\s+/g, '_')}.pdf`
                      : selectorUrl.split('/').pop()
                  }
                  subjectName={selectedSubjectName}
                  processSelectorId={processId}
                  onComplete={handlePdfComplete}
                />
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="w-full bg-white dark:bg-slate-800 rounded-2xl p-8 text-center border border-gray-200 dark:border-slate-700 shadow-lg"
              >
                <motion.div
                  animate={
                    shouldReduceMotion
                      ? {}
                      : {
                          rotate: [0, 15, -15, 0],
                          y: [0, -8, 8, 0],
                          transition: { repeat: Infinity, duration: 4, ease: 'easeInOut' }
                        }
                  }
                  className="w-24 h-24 rounded-full mx-auto mb-6 flex items-center justify-center bg-yellow-400/20 text-yellow-500 dark:bg-yellow-300/20 dark:text-yellow-300"
                >
                  <ScanSearch size={40} />
                </motion.div>
                <h2 className="text-2xl font-semibold mb-4 text-slate-800 dark:text-slate-200">
                  Content Not Found
                </h2>
                <p className="mb-8 max-w-lg mx-auto text-gray-600 dark:text-gray-300 text-lg">
                  We couldn't locate the study materials for this topic. Our team has been notified
                  and will add content soon.
                </p>
                <div className="flex justify-center space-x-4">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleBack}
                    className="px-6 py-3 rounded-full text-base font-medium bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-500 dark:hover:bg-blue-600 transition-colors"
                  >
                    Return to Topics
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => window.location.reload()}
                    className="px-6 py-3 rounded-full text-base font-medium border-2 border-blue-600 hover:bg-blue-50 text-blue-600 dark:border-blue-400 dark:text-blue-400 dark:hover:bg-slate-700 transition-colors"
                  >
                    Try Again
                  </motion.button>
                </div>
              </motion.div>
            )}
          </motion.div>
        )}
      </main>

      {currentView !== 'subjects' && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          className="fixed bottom-6 right-6 z-40 md:hidden"
        >
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={handleBack}
            className="w-14 h-14 rounded-full flex items-center justify-center shadow-lg bg-blue-600 text-white dark:bg-blue-400"
          >
            <ChevronLeft size={20} />
          </motion.button>
        </motion.div>
      )}
    </div>
  );
};

export default AiTutor;
