import React, { useState, useRef } from 'react';
import { Link, useNavigate } from 'react-router';
import { useDispatch } from 'react-redux';
import Logo from '../../assets/sasthra_logo.png';
import { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion';
import {
  Eye,
  EyeOff,
  Lock,
  User,
  ArrowRight,
  BrainCircuit,
  ArrowLeft,
  BookOpen,
  FlaskConical,
  Calculator
} from 'lucide-react';

import Input from '../../components/Field/Input';
import Button from '../../components/Field/Button';
import Toastify from '../../components/PopUp/Toastify';

import {
  setAuthData,
  useForgotPasswordServiceMutation,
  useResetPasswordServiceMutation,
  useUserLoginServiceMutation,
  useVerfiyOptServiceMutation
} from './auth.slice';

const Login = () => {
  const [userName, setUserName] = useState('');
  const [password, setPassword] = useState('');
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showOtp, setShowOtp] = useState(false);
  const [res, setRes] = useState(null);
  const [isEmail, setIsEmail] = useState(false);
  const [reset, setReset] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [resetOtp, setResetOtp] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmNewPassword, setConfirmNewPassword] = useState('');

  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [userLoginService, { isLoading: isLoginLoading }] = useUserLoginServiceMutation();
  const [otpVerifyService, { isLoading: isOtpLoading }] = useVerfiyOptServiceMutation();
  const [forgotPasswordService, { isLoading: isForgotLoading }] =
    useForgotPasswordServiceMutation();
  const [resetPasswordService, { isLoading: isResetLoading }] = useResetPasswordServiceMutation();

  const isLoading = isLoginLoading || isOtpLoading || isForgotLoading || isResetLoading;

  const colors = {
    saffron: 'var(--color-teacher)',
    white: '#FFFFFF',
    green: 'var(--color-counselor)',
    navy: '#000080',
    gold: '#FFD700',
    teal: '#008080'
  };

  const containerRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ['start start', 'end start']
  });

  const yBg = useTransform(scrollYProgress, [0, 1], ['0%', '50%']);

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await userLoginService({ username: userName, password }).unwrap();
      setShowOtp(true);
    } catch (err) {
      console.error('Login failed:', err);
      setRes(err);
      setShowOtp(false);
    }
  };

  const handleOtpVerify = async (e) => {
    e.preventDefault();
    try {
      const res = await otpVerifyService({ otp: otp }).unwrap();
      dispatch(setAuthData(res));
      sessionStorage.setItem('userId', res.user.id);
      sessionStorage.setItem('name', `${res.user.first_name} ${res.user.last_name}`);
      sessionStorage.setItem('role', res.user.role);
      sessionStorage.setItem('token', res.token);
      sessionStorage.setItem('centername', res.user.center_name);
      sessionStorage.setItem('phone', res.user.phone);
      sessionStorage.setItem('centercode', res.user.center_code);
      sessionStorage.setItem('course', res.user.course);
      sessionStorage.setItem('batch', res.user.batch_id);
      sessionStorage.setItem('batchname', res.user.batch_name);
      sessionStorage.setItem('designation', res.user.designation);

      navigate('/sasthra');
    } catch (err) {
      setRes(err);
    }
  };

  const handleForgotPassword = async (e) => {
    e.preventDefault();
    try {
      const res = await forgotPasswordService({ email }).unwrap();
      setRes(res);
      setReset(true);
    } catch (error) {
      setRes(error);
    }
  };

  const handleResetPassword = async (e) => {
    e.preventDefault();
    if (newPassword !== confirmNewPassword) {
      setRes({
        status: 'error',
        message: 'Passwords do not match',
        errors: { confirmNewPassword: 'Passwords do not match' }
      });
      return;
    }
    try {
      const res = await resetPasswordService({
        email: resetEmail,
        otp: resetOtp,
        new_password: newPassword
      }).unwrap();
      setRes(res);
      setReset(false);
      setResetOtp('');
      setNewPassword('');
      setConfirmNewPassword('');
    } catch (error) {
      setRes(error);
    }
  };

  const formVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        when: 'beforeChildren',
        staggerChildren: 0.15,
        type: 'spring',
        stiffness: 100
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const otpInputVariants = {
    hidden: { opacity: 0, height: 0, y: -10 },
    visible: { opacity: 1, height: 'auto', y: 0, transition: { duration: 0.4 } },
    exit: { opacity: 0, height: 0, y: -10, transition: { duration: 0.4 } }
  };

  const loadingVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { duration: 0.5 }
    },
    exit: { opacity: 0, transition: { duration: 0.3 } }
  };

  const logoGlow = {
    scale: [1, 1.05, 1],
    opacity: [0.9, 1, 0.9],
    boxShadow: [
      '0 0 10px rgba(0, 128, 128, 0.5)',
      '0 0 20px rgba(0, 128, 128, 0.8)',
      '0 0 10px rgba(0, 128, 128, 0.5)'
    ],
    transition: { repeat: Infinity, duration: 2 }
  };

  const particleVariants = {
    animate: {
      rotate: 360,
      transition: { repeat: Infinity, duration: 4, ease: 'linear' }
    }
  };

  const typewriterVariants = {
    hidden: { width: 0 },
    visible: {
      width: 'auto',
      transition: { duration: 1, ease: 'easeInOut' }
    }
  };

  const getFieldError = (field) => {
    if (res?.status === 'error' && res?.errors?.[field]) {
      return res.errors[field];
    }
    return null;
  };

  return (
    <div
      ref={containerRef}
      className="min-h-screen overflow-y-auto relative"
      style={{ perspective: '1000px' }}
    >
      {/* Parallax Background */}
      <motion.div
        className="fixed inset-0 bg-gray-900 z-0"
        style={{
          y: yBg,
          backgroundImage:
            "url('https://images.unsplash.com/photo-1635070041078-e363dbe005cb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')",
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        <div className="absolute inset-0 bg-[#000080] opacity-90"></div>
      </motion.div>

      {/* Loading Overlay */}
      <AnimatePresence>
        {isLoading && (
          <motion.div
            className="fixed inset-0 z-50 flex flex-col items-center justify-center"
            style={{
              background: 'var(--color-teacher)'
            }}
            variants={loadingVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            {/* Rotating Particle Rings */}
            <motion.div
              className="absolute w-48 h-48"
              variants={particleVariants}
              animate="animate"
            >
              {[...Array(8)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-3 h-3 bg-[var(--color-gold)] rounded-full"
                  style={{
                    top: '50%',
                    left: '50%',
                    transform: `rotate(${i * 45}deg) translate(80px)`
                  }}
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.6, 1, 0.6],
                    transition: { repeat: Infinity, duration: 1.5, delay: i * 0.1 }
                  }}
                />
              ))}
            </motion.div>

            {/* Glowing Logo */}
            <motion.img
              src={Logo}
              alt="Sasthra Logo"
              className="w-82 h-52 mb-8 rounded-full"
              animate={logoGlow}
            />

            {/* Typewriter Effect Text */}
            <motion.div
              className="text-white text-2xl font-medium overflow-hidden whitespace-nowrap"
              variants={typewriterVariants}
              initial="hidden"
              animate="visible"
            >
              {reset
                ? 'Resetting Password...'
                : showOtp
                  ? 'Verifying OTP...'
                  : isEmail
                    ? 'Sending OTP...'
                    : 'Logging in to Sasthra...'}
            </motion.div>

            {/* Progress Bar */}
            <motion.div
              className="mt-6 w-64 h-2 bg-white/20 rounded-full overflow-hidden"
              initial={{ width: 0 }}
              animate={{ width: '100%' }}
              transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
            >
              <div className="h-full bg-[var(--color-saffron)]" />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <motion.button
          onClick={() => navigate('/')}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
          className="fixed top-6 right-6 z-50 flex items-center gap-2 hover:cursor-pointer bg-white/90 px-4 py-2 rounded-full border border-gray-200 shadow-md"
          whileHover={{ scale: 1.05, color: `var(--color-trainee)` }}
          whileTap={{ scale: 0.95 }}
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10 19l-7-7m0 0l7-7m-7 7h18"
            />
          </svg>
          <span className="font-medium">Back</span>
        </motion.button>

        <div className="w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Branding */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="text-white"
          >
            <motion.div className="flex items-center gap-4 mb-6" whileHover={{ scale: 1.02 }}>
              <motion.div>
                <img src={Logo} alt="Sasthra Logo" />
              </motion.div>
            </motion.div>

            <motion.p
              className="text-xl mb-8 max-w-md"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              Revolutionizing education with AI-powered learning solutions
            </motion.p>

            <motion.div
              className="grid grid-cols-2 hover:cursor-pointer gap-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              {[
                { icon: <BookOpen className="w-6 h-6" />, text: 'JEE Prep' },
                { icon: <FlaskConical className="w-6 h-6" />, text: 'NEET Prep' },
                { icon: <Calculator className="w-6 h-6" />, text: 'CBSE' },
                { icon: <User className="w-6 h-6" />, text: 'Mentorship' }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  className="bg-white/10 p-4 rounded-xl backdrop-blur-sm border border-white/20"
                  whileHover={{ y: -5, scale: 1.05 }}
                  transition={{ type: 'spring', stiffness: 300 }}
                >
                  <div className="flex items-center gap-3">
                    {item.icon}
                    <span>{item.text}</span>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Right Column - Login Form */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="bg-white rounded-2xl shadow-2xl overflow-hidden"
          >
            <div className="p-8">
              <Toastify res={res} resClear={() => setRes(null)} />

              <motion.div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-[var(--color-teacher)] mb-2">
                  {reset
                    ? 'Reset Password'
                    : showOtp
                      ? 'Verify OTP'
                      : isEmail
                        ? 'Forgot Password'
                        : 'Welcome Back'}
                </h2>
                <p className="text-gray-600">
                  {reset
                    ? 'Set your new password'
                    : showOtp
                      ? 'Enter the OTP sent to your email'
                      : isEmail
                        ? "We'll help you reset your password"
                        : 'Login to continue your journey'}
                </p>
              </motion.div>

              <form
                onSubmit={
                  reset
                    ? handleResetPassword
                    : showOtp
                      ? handleOtpVerify
                      : isEmail
                        ? handleForgotPassword
                        : handleSubmit
                }
              >
                <div className="space-y-5">
                  {reset ? (
                    <>
                      <Input
                        type="email"
                        value={resetEmail}
                        onChange={(e) => setResetEmail(e.target.value)}
                        placeholder="Email"
                        leftIcon={<User className="h-5 w-5 text-gray-500" />}
                        className="w-full pl-12 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#000080] focus:border-[#000080] transition-all"
                      />
                      <Input
                        type="number"
                        value={resetOtp}
                        onChange={(e) => setResetOtp(e.target.value)}
                        placeholder="OTP"
                        leftIcon={<Lock className="h-5 w-5 text-gray-500" />}
                        className="w-full pl-12 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#000080] focus:border-[#000080] transition-all"
                      />
                      <Input
                        type={showPassword ? 'text' : 'password'}
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        placeholder="New Password"
                        leftIcon={<Lock className="h-5 w-5 text-gray-500" />}
                        rightIcon={
                          showPassword ? (
                            <EyeOff
                              className="h-5 w-5 text-gray-500 cursor-pointer"
                              onClick={() => setShowPassword(!showPassword)}
                            />
                          ) : (
                            <Eye
                              className="h-5 w-5 text-gray-500 cursor-pointer"
                              onClick={() => setShowPassword(!showPassword)}
                            />
                          )
                        }
                        className="w-full pl-12 pr-12 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#000080] focus:border-[#000080] transition-all"
                      />
                      <Input
                        type={showPassword ? 'text' : 'password'}
                        value={confirmNewPassword}
                        onChange={(e) => setConfirmNewPassword(e.target.value)}
                        placeholder="Confirm Password"
                        leftIcon={<Lock className="h-5 w-5 text-gray-500" />}
                        className="w-full pl-12 pr-12 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#000080] focus:border-[#000080] transition-all"
                      />
                    </>
                  ) : !isEmail ? (
                    <>
                      <Input
                        type="text"
                        value={userName}
                        onChange={(e) => setUserName(e.target.value)}
                        placeholder="Username"
                        leftIcon={<User className="h-5 w-5 text-gray-500" />}
                        className="w-full pl-12 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#000080] focus:border-[#000080] transition-all"
                      />
                      <Input
                        type={showPassword ? 'text' : 'password'}
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Password"
                        leftIcon={<Lock className="h-5 w-5 text-gray-500" />}
                        rightIcon={
                          showPassword ? (
                            <EyeOff
                              className="h-5 w-5 text-gray-500 cursor-pointer"
                              onClick={() => setShowPassword(!showPassword)}
                            />
                          ) : (
                            <Eye
                              className="h-5 w-5 text-gray-500 cursor-pointer"
                              onClick={() => setShowPassword(!showPassword)}
                            />
                          )
                        }
                        className="w-full pl-12 pr-12 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#000080] focus:border-[#000080] transition-all"
                      />
                    </>
                  ) : (
                    <Input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="Email"
                      leftIcon={<User className="h-5 w-5 text-gray-500" />}
                      className="w-full pl-12 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#000080] focus:border-[#000080] transition-all"
                    />
                  )}

                  <AnimatePresence>
                    {!reset && showOtp && (
                      <motion.div
                        key="otp-input"
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Input
                          type="number"
                          value={otp}
                          onChange={(e) => setOtp(e.target.value)}
                          placeholder="OTP"
                          leftIcon={<Lock className="h-5 w-5 text-gray-500" />}
                          className="w-full pl-12 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-[#000080] focus:border-[#000080] transition-all mt-4"
                        />
                      </motion.div>
                    )}
                  </AnimatePresence>

                  <div className="flex items-center justify-between">
                    {isEmail || reset ? (
                      <button
                        type="button"
                        onClick={() => {
                          setIsEmail(false);
                          setReset(false);
                          setEmail('');
                          setResetOtp('');
                          setNewPassword('');
                          setConfirmNewPassword('');
                          setRes(null);
                        }}
                        className="flex items-center text-sm text-gray-600 hover:text-gray-900"
                      >
                        <ArrowLeft className="w-4 h-4 mr-1" />
                        Back to login
                      </button>
                    ) : (
                      <button
                        type="button"
                        onClick={() => setIsEmail(true)}
                        className="text-sm text-gray-600 hover:text-gray-900"
                      >
                        Forgot password?
                      </button>
                    )}
                  </div>

                  <motion.button
                    type="submit"
                    disabled={isLoading}
                    className="w-full py-3 px-6 hover:cursor-pointer rounded-lg font-medium text-white transition-all duration-300 shadow-md hover:shadow-lg"
                    style={{ backgroundColor: '#000080' }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {isLoading
                      ? 'Processing...'
                      : reset
                        ? 'Reset Password'
                        : showOtp
                          ? 'Verify OTP'
                          : isEmail
                            ? 'Send OTP'
                            : 'Login to Sasthra'}
                  </motion.button>
                </div>
              </form>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default Login;
